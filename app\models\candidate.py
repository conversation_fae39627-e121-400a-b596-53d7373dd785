"""
Candidate model for storing candidate information and profiles.
"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class Candidate(Base):
    """
    Candidate model for storing candidate profiles and metadata.
    """
    __tablename__ = "candidates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Basic information
    email = Column(String(255), nullable=False, index=True)
    full_name = Column(String(255), nullable=False)
    phone = Column(String(50))
    
    # Online presence
    linkedin_url = Column(String(500))
    github_url = Column(String(500))
    portfolio_url = Column(String(500))
    
    # Location information
    current_location = Column(String(255))
    desired_location = Column(String(255))
    
    # Professional information
    years_experience = Column(Integer)
    current_role = Column(String(255))
    current_company = Column(String(255))
    desired_role = Column(String(255))
    
    # Skills and keywords (stored as array)
    skills = Column(ARRAY(String))
    industries = Column(ARRAY(String))
    
    # Additional information
    notes = Column(Text)
    summary = Column(Text)  # AI-generated summary
    
    # Status tracking
    is_active = Column(Boolean, default=True)
    has_resume = Column(Boolean, default=False)
    
    # Metadata
    source = Column(String(100))  # How candidate was added
    added_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    documents = relationship("Document", back_populates="candidate", cascade="all, delete-orphan")
    scores = relationship("CandidateScore", back_populates="candidate", cascade="all, delete-orphan")
    outreach_emails = relationship("OutreachEmail", back_populates="candidate", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Candidate(id={self.id}, name={self.full_name}, email={self.email})>"
    
    @property
    def experience_level(self) -> str:
        """Determine experience level based on years of experience."""
        if not self.years_experience:
            return "unknown"
        elif self.years_experience < 2:
            return "entry"
        elif self.years_experience < 5:
            return "mid"
        elif self.years_experience < 10:
            return "senior"
        else:
            return "executive"
