"""
Core application configuration using Pydantic Settings.
Handles environment variables, validation, and configuration management.
"""

from functools import lru_cache
from typing import List, Optional
from pydantic import validator
import os

try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # Fallback for older pydantic versions
        class BaseSettings:
            pass


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    url: str = "postgresql://rag_user:rag_password@localhost:5432/rag_db"
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10
    
    class Config:
        env_prefix = "DATABASE_"


class VectorDBSettings(BaseSettings):
    """Vector database configuration."""
    
    # ChromaDB settings
    chroma_persist_directory: str = "./data/chroma"
    chroma_host: str = "localhost"
    chroma_port: int = 8001
    
    # Pinecone settings (alternative)
    pinecone_api_key: Optional[str] = None
    pinecone_environment: Optional[str] = None
    pinecone_index_name: str = "recruitment-rag"
    
    class Config:
        env_prefix = "CHROMA_"


class LLMSettings(BaseSettings):
    """Large Language Model configuration."""
    
    # Provider selection
    llm_provider: str = "openai"  # Options: "openai", "lmstudio", "ollama", "anthropic"
    
    # OpenAI settings
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-4-1106-preview"
    openai_embedding_model: str = "text-embedding-3-large"
    openai_max_tokens: int = 4096
    openai_temperature: float = 0.1
    
    # LM Studio settings
    lmstudio_host: str = "http://localhost:1234"
    lmstudio_api_key: str = "lm-studio"  # LM Studio uses this default
    lmstudio_model: str = "local-model"
    lmstudio_embedding_model: Optional[str] = None  # Some models support embeddings
    lmstudio_max_tokens: int = 4096
    lmstudio_temperature: float = 0.1
    
    # Anthropic settings (alternative)
    anthropic_api_key: Optional[str] = None
    
    # Local LLM settings (Ollama)
    ollama_host: Optional[str] = None
    ollama_model: Optional[str] = None
    
    @validator("openai_api_key")
    def validate_openai_key(cls, v, values):
        provider = values.get("llm_provider", "openai")
        if provider == "openai" and (not v or v == "your-openai-api-key-here"):
            raise ValueError("OpenAI API key must be set when using OpenAI provider")
        return v
    
    @validator("lmstudio_host")
    def validate_lmstudio_host(cls, v, values):
        provider = values.get("llm_provider", "openai")
        if provider == "lmstudio" and not v:
            raise ValueError("LM Studio host must be set when using LM Studio provider")
        return v
    
    class Config:
        env_prefix = "LLM_"


class RedisSettings(BaseSettings):
    """Redis and Celery configuration."""
    
    url: str = "redis://localhost:6379/0"
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"
    
    class Config:
        env_prefix = "REDIS_"


class SecuritySettings(BaseSettings):
    """Security and authentication settings."""
    
    secret_key: str
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    algorithm: str = "HS256"
    
    @validator("secret_key")
    def validate_secret_key(cls, v):
        if not v or v == "your-super-secret-key-here-change-in-production":
            raise ValueError("Secret key must be set and changed from default")
        if len(v) < 32:
            raise ValueError("Secret key must be at least 32 characters long")
        return v
    
    class Config:
        env_prefix = ""


class FileStorageSettings(BaseSettings):
    """File upload and storage configuration."""
    
    upload_dir: str = "./data/uploads"
    max_file_size_mb: int = 50
    allowed_extensions: List[str] = ["pdf", "docx", "doc", "txt", "md", "html"]
    
    @validator("allowed_extensions", pre=True)
    def parse_extensions(cls, v):
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(",")]
        return v
    
    class Config:
        env_prefix = ""


class ProcessingSettings(BaseSettings):
    """Document processing and RAG configuration."""
    
    # Text chunking
    chunk_size: int = 1000
    chunk_overlap: int = 200
    max_chunks_per_document: int = 100
    
    # RAG retrieval
    top_k_retrieval: int = 5
    similarity_threshold: float = 0.7
    max_context_length: int = 8000
    
    class Config:
        env_prefix = ""


class MonitoringSettings(BaseSettings):
    """Monitoring and logging configuration."""
    
    log_level: str = "INFO"
    log_format: str = "json"
    enable_metrics: bool = True
    metrics_port: int = 9090
    sentry_dsn: Optional[str] = None
    
    class Config:
        env_prefix = ""


class RateLimitSettings(BaseSettings):
    """Rate limiting configuration."""
    
    enabled: bool = True
    requests_per_minute: int = 60
    burst: int = 10
    
    class Config:
        env_prefix = "RATE_LIMIT_"


class Settings(BaseSettings):
    """Main application settings."""
    
    # Application info
    app_name: str = "Recruitment RAG System"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    
    # API configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_prefix: str = "/api/v1"
    
    # Component settings
    database: DatabaseSettings = DatabaseSettings()
    vectordb: VectorDBSettings = VectorDBSettings()
    llm: LLMSettings = LLMSettings()
    redis: RedisSettings = RedisSettings()
    security: SecuritySettings = SecuritySettings()
    file_storage: FileStorageSettings = FileStorageSettings()
    processing: ProcessingSettings = ProcessingSettings()
    monitoring: MonitoringSettings = MonitoringSettings()
    rate_limit: RateLimitSettings = RateLimitSettings()
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Ensure upload directory exists
        os.makedirs(self.file_storage.upload_dir, exist_ok=True)
        os.makedirs(self.vectordb.chroma_persist_directory, exist_ok=True)


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings.
    Uses lru_cache to ensure settings are loaded only once.
    """
    return Settings()
