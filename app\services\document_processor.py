"""
Document Processing Service for handling file uploads and text extraction.
Supports PDF, DOCX, TXT, and other document formats.
"""

import asyncio
import os
import hashlib
from typing import Dict, List, Any, Optional, Tuple, BinaryIO
import fitz  # PyMuPDF
import pdfplumber
from docx import Document as DocxDocument
import tempfile
import time
from pathlib import Path

from app.core.config import get_settings
from app.core.exceptions import DocumentProcessingError, ValidationError
from app.core.logging import get_rag_logger

logger = get_rag_logger(__name__)


class DocumentProcessor:
    """
    Document processing service for text extraction and chunking.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.allowed_extensions = set(self.settings.file_storage.allowed_extensions)
        self.max_file_size = self.settings.file_storage.max_file_size_mb * 1024 * 1024
        self.chunk_size = self.settings.processing.chunk_size
        self.chunk_overlap = self.settings.processing.chunk_overlap
    
    def validate_file(self, filename: str, file_size: int) -> None:
        """
        Validate uploaded file.
        
        Args:
            filename: Original filename
            file_size: File size in bytes
            
        Raises:
            ValidationError: If file is invalid
        """
        # Check file extension
        file_extension = Path(filename).suffix.lower().lstrip('.')
        if file_extension not in self.allowed_extensions:
            raise ValidationError(
                f"File type '{file_extension}' not allowed. Allowed types: {', '.join(self.allowed_extensions)}",
                details={"filename": filename, "extension": file_extension}
            )
        
        # Check file size
        if file_size > self.max_file_size:
            raise ValidationError(
                f"File size {file_size / (1024*1024):.1f}MB exceeds maximum allowed size of {self.settings.file_storage.max_file_size_mb}MB",
                details={"filename": filename, "size_mb": file_size / (1024*1024)}
            )
    
    def calculate_file_hash(self, file_content: bytes) -> str:
        """
        Calculate SHA-256 hash of file content.
        
        Args:
            file_content: File content as bytes
            
        Returns:
            str: Hex digest of file hash
        """
        return hashlib.sha256(file_content).hexdigest()
    
    async def save_uploaded_file(
        self,
        file_content: bytes,
        filename: str,
        candidate_id: Optional[str] = None
    ) -> Tuple[str, str]:
        """
        Save uploaded file to storage.
        
        Args:
            file_content: File content as bytes
            filename: Original filename
            candidate_id: Optional candidate ID for organization
            
        Returns:
            Tuple[str, str]: (file_path, file_hash)
        """
        try:
            # Generate unique filename
            file_hash = self.calculate_file_hash(file_content)
            file_extension = Path(filename).suffix.lower()
            unique_filename = f"{file_hash}{file_extension}"
            
            # Organize by candidate if provided
            if candidate_id:
                file_dir = Path(self.settings.file_storage.upload_dir) / candidate_id
            else:
                file_dir = Path(self.settings.file_storage.upload_dir) / "general"
            
            file_dir.mkdir(parents=True, exist_ok=True)
            file_path = file_dir / unique_filename
            
            # Save file
            with open(file_path, "wb") as f:
                f.write(file_content)
            
            logger.info(
                "File saved successfully",
                filename=filename,
                file_path=str(file_path),
                file_size=len(file_content)
            )
            
            return str(file_path), file_hash
            
        except Exception as e:
            raise DocumentProcessingError(
                f"Failed to save file: {str(e)}",
                filename=filename
            )
    
    async def extract_text_from_pdf(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from PDF file.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            dict: Extracted text and metadata
        """
        start_time = time.time()
        
        try:
            text_content = ""
            page_texts = []
            metadata = {"pages": 0, "method": "pdfplumber"}
            
            # Use pdfplumber for better text extraction
            with pdfplumber.open(file_path) as pdf:
                metadata["pages"] = len(pdf.pages)
                
                for page_num, page in enumerate(pdf.pages, 1):
                    page_text = page.extract_text() or ""
                    page_texts.append({
                        "page_number": page_num,
                        "text": page_text,
                        "char_count": len(page_text)
                    })
                    text_content += f"\n--- Page {page_num} ---\n{page_text}\n"
                
                # Extract additional metadata
                if pdf.metadata:
                    metadata.update({
                        "title": pdf.metadata.get("Title", ""),
                        "author": pdf.metadata.get("Author", ""),
                        "subject": pdf.metadata.get("Subject", ""),
                        "creator": pdf.metadata.get("Creator", "")
                    })
            
            processing_time = time.time() - start_time
            
            result = {
                "text": text_content.strip(),
                "page_texts": page_texts,
                "metadata": metadata,
                "processing_time_seconds": processing_time,
                "character_count": len(text_content),
                "language": "en"  # TODO: Add language detection
            }
            
            logger.log_document_processing(
                filename=Path(file_path).name,
                status="completed",
                processing_time=processing_time
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"PDF text extraction failed: {str(e)}"
            
            logger.log_document_processing(
                filename=Path(file_path).name,
                status="failed",
                processing_time=processing_time,
                error=error_msg
            )
            
            raise DocumentProcessingError(error_msg, filename=Path(file_path).name)
    
    async def extract_text_from_docx(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from DOCX file.
        
        Args:
            file_path: Path to DOCX file
            
        Returns:
            dict: Extracted text and metadata
        """
        start_time = time.time()
        
        try:
            doc = DocxDocument(file_path)
            
            # Extract paragraphs
            paragraphs = []
            text_content = ""
            
            for para in doc.paragraphs:
                para_text = para.text.strip()
                if para_text:
                    paragraphs.append(para_text)
                    text_content += para_text + "\n"
            
            # Extract metadata
            metadata = {
                "paragraphs_count": len(paragraphs),
                "method": "python-docx"
            }
            
            # Core properties
            if doc.core_properties:
                metadata.update({
                    "title": doc.core_properties.title or "",
                    "author": doc.core_properties.author or "",
                    "subject": doc.core_properties.subject or "",
                    "created": str(doc.core_properties.created) if doc.core_properties.created else "",
                    "modified": str(doc.core_properties.modified) if doc.core_properties.modified else ""
                })
            
            processing_time = time.time() - start_time
            
            result = {
                "text": text_content.strip(),
                "paragraphs": paragraphs,
                "metadata": metadata,
                "processing_time_seconds": processing_time,
                "character_count": len(text_content),
                "language": "en"  # TODO: Add language detection
            }
            
            logger.log_document_processing(
                filename=Path(file_path).name,
                status="completed",
                processing_time=processing_time
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"DOCX text extraction failed: {str(e)}"
            
            logger.log_document_processing(
                filename=Path(file_path).name,
                status="failed",
                processing_time=processing_time,
                error=error_msg
            )
            
            raise DocumentProcessingError(error_msg, filename=Path(file_path).name)
    
    async def extract_text_from_txt(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from plain text file.
        
        Args:
            file_path: Path to text file
            
        Returns:
            dict: Extracted text and metadata
        """
        start_time = time.time()
        
        try:
            # Try to read the file with utf-8 encoding
            text_content = ""
            encoding_used = "utf-8"
            
            try:
                with open(file_path, 'r', encoding=encoding_used) as f:
                    text_content = f.read()
            except UnicodeDecodeError as e:
                raise ValueError(f"Could not decode file with {encoding_used} encoding: {e}")
            
            
            processing_time = time.time() - start_time
            
            result = {
                "text": text_content.strip(),
                "metadata": {
                    "encoding": encoding_used,
                    "method": "plain_text"
                },
                "processing_time_seconds": processing_time,
                "character_count": len(text_content),
                "language": "en"  # TODO: Add language detection
            }
            
            logger.log_document_processing(
                filename=Path(file_path).name,
                status="completed",
                processing_time=processing_time
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Text file extraction failed: {str(e)}"
            
            logger.log_document_processing(
                filename=Path(file_path).name,
                status="failed",
                processing_time=processing_time,
                error=error_msg
            )
            
            raise DocumentProcessingError(error_msg, filename=Path(file_path).name)
    
    async def extract_text(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from document based on file type.
        
        Args:
            file_path: Path to document file
            
        Returns:
            dict: Extracted text and metadata
        """
        file_extension = Path(file_path).suffix.lower()
        
        if file_extension == '.pdf':
            return await self.extract_text_from_pdf(file_path)
        elif file_extension in ['.docx', '.doc']:
            return await self.extract_text_from_docx(file_path)
        elif file_extension in ['.txt', '.md']:
            return await self.extract_text_from_txt(file_path)
        else:
            raise DocumentProcessingError(
                f"Unsupported file type: {file_extension}",
                filename=Path(file_path).name
            )
    
    def create_text_chunks(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Split text into chunks for embedding.
        
        Args:
            text: Text content to chunk
            metadata: Optional metadata to include with chunks
            
        Returns:
            List[Dict]: List of text chunks with metadata
        """
        chunks = []
        text_length = len(text)
        
        # Simple character-based chunking
        start = 0
        chunk_index = 0
        
        while start < text_length:
            end = start + self.chunk_size
            
            # Adjust end to avoid breaking words
            if end < text_length:
                # Find the last space or punctuation within the chunk
                for i in range(end, max(start, end - 100), -1):
                    if text[i] in [' ', '\n', '.', '!', '?', ';']:
                        end = i + 1
                        break
            
            chunk_text = text[start:end].strip()
            
            if chunk_text:
                chunk = {
                    "content": chunk_text,
                    "chunk_index": chunk_index,
                    "start_char": start,
                    "end_char": end,
                    "content_length": len(chunk_text)
                }
                
                # Add metadata if provided
                if metadata:
                    chunk.update(metadata)
                
                chunks.append(chunk)
                chunk_index += 1
            
            # Move start position with overlap
            start = max(start + 1, end - self.chunk_overlap)
        
        return chunks
    
    async def process_document(
        self,
        file_content: bytes,
        filename: str,
        document_type: str = "resume",
        candidate_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Complete document processing pipeline.
        
        Args:
            file_content: File content as bytes
            filename: Original filename
            document_type: Type of document
            candidate_id: Optional candidate ID
            
        Returns:
            dict: Processing results with text and chunks
        """
        start_time = time.time()
        
        try:
            # Validate file
            self.validate_file(filename, len(file_content))
            
            # Save file
            file_path, file_hash = await self.save_uploaded_file(
                file_content, filename, candidate_id
            )
            
            # Extract text
            extraction_result = await self.extract_text(file_path)
            
            # Create chunks
            chunk_metadata = {
                "document_type": document_type,
                "source": "upload",
                "candidate_id": candidate_id
            }
            
            chunks = self.create_text_chunks(
                extraction_result["text"],
                chunk_metadata
            )
            
            total_time = time.time() - start_time
            
            result = {
                "file_path": file_path,
                "file_hash": file_hash,
                "file_size": len(file_content),
                "extracted_text": extraction_result["text"],
                "text_length": len(extraction_result["text"]),
                "chunks": chunks,
                "chunks_created": len(chunks),
                "metadata": extraction_result["metadata"],
                "processing_time_seconds": total_time,
                "language": extraction_result.get("language", "en"),
                "document_type": document_type
            }
            
            logger.log_document_processing(
                filename=filename,
                status="completed",
                chunks_created=len(chunks),
                processing_time=total_time
            )
            
            return result
            
        except Exception as e:
            total_time = time.time() - start_time
            
            if isinstance(e, (ValidationError, DocumentProcessingError)):
                raise
            
            error_msg = f"Document processing failed: {str(e)}"
            logger.log_document_processing(
                filename=filename,
                status="failed",
                processing_time=total_time,
                error=error_msg
            )
            
            raise DocumentProcessingError(error_msg, filename=filename)


# Global service instance
_document_processor: Optional[DocumentProcessor] = None


def get_document_processor() -> DocumentProcessor:
    """
    Get or create document processor instance.
    
    Returns:
        DocumentProcessor: Processor instance
    """
    global _document_processor
    
    if _document_processor is None:
        _document_processor = DocumentProcessor()
    
    return _document_processor
