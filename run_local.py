#!/usr/bin/env python3
"""
Simple local development server for testing without <PERSON><PERSON>.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set environment variables for local development
os.environ.setdefault("DEBUG", "true")
os.environ.setdefault("ENVIRONMENT", "development")
os.environ.setdefault("DATABASE_URL", "sqlite:///./rag_dev.db")
os.environ.setdefault("CHROMA_PERSIST_DIRECTORY", "./data/chroma")
os.environ.setdefault("REDIS_URL", "redis://localhost:6379/0")
os.environ.setdefault("SECRET_KEY", "dev-secret-key-not-for-production")
os.environ.setdefault("OPENAI_API_KEY", "your-api-key-here")

try:
    import uvicorn
    from app.main import app
    
    print("🚀 Starting RAG System in development mode...")
    print("📖 API Documentation: http://localhost:8000/api/v1/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("⚠️  Note: Some features require database and external services")
    print("")
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
    
except ImportError as e:
    print(f"❌ Missing dependencies: {e}")
    print("💡 Install with: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ Failed to start server: {e}")
    sys.exit(1)
