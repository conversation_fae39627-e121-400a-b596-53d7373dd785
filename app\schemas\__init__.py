"""
Pydantic schemas for request/response validation.
"""

from app.schemas.auth import *
from app.schemas.candidate import *
from app.schemas.job import *
from app.schemas.scoring import *
from app.schemas.outreach import *

__all__ = [
    # Auth schemas
    "UserCreate", "UserLogin", "Token", "UserResponse",
    
    # Candidate schemas  
    "CandidateCreate", "CandidateUpdate", "CandidateResponse", "ResumeUpload",
    
    # Job schemas
    "JobCreate", "JobUpdate", "JobResponse", "JobRequirementsAnalysis",
    
    # Scoring schemas
    "RequirementScore", "ScoringRequest", "ScoringResponse", "BatchScoringRequest",
    "ScoringComparison", "ScoringHistory", "ScoreExplanation",
    
    # Outreach schemas
    "EmailRequest", "EmailResponse", "BatchEmailRequest", "EmailTemplate",
    "EmailTemplateCreate", "EmailTemplateUpdate", "EmailHistory", "EmailAnalytics",
    "ToneAnalysis", "EmailSendRequest", "EmailSendResponse"
]
