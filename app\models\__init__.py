"""
Models package initialization.
Imports all models to ensure they are registered with SQLAlchemy.
"""

from app.models.user import User
from app.models.candidate import Candidate
from app.models.job_posting import JobPosting
from app.models.document import Document, DocumentChunk
from app.models.scoring import CandidateScore, RequirementScore
from app.models.email_template import EmailTemplate, OutreachEmail, EmailAnalytics

# Export all models for easy importing
__all__ = [
    "User",
    "Candidate", 
    "JobPosting",
    "Document",
    "DocumentChunk",
    "CandidateScore",
    "RequirementScore",
    "EmailTemplate",
    "OutreachEmail",
    "EmailAnalytics"
]
