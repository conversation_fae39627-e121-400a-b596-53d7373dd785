<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
        }
        .feature-card h3 {
            margin-top: 0;
            color: #333;
        }
        .api-endpoints {
            margin: 30px 0;
        }
        .endpoint {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RAG Application</h1>
        
        <div id="status" class="status disconnected">
            <h2>System Status: Disconnected</h2>
            <p>Waiting for system to start...</p>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>Document Processing</h3>
                <p>Upload and process documents for RAG</p>
            </div>
            <div class="feature-card">
                <h3>AI Retrieval</h3>
                <p>Query your document database with AI</p>
            </div>
            <div class="feature-card">
                <h3>Scoring Engine</h3>
                <p>Automated candidate scoring</p>
            </div>
        </div>

        <div class="api-endpoints">
            <h2>API Endpoints</h2>
            <div class="endpoint">POST /api/v1/documents/upload</div>
            <div class="endpoint">GET /api/v1/documents/list</div>
            <div class="endpoint">POST /api/v1/query</div>
            <div class="endpoint">GET /api/v1/health</div>
        </div>

        <div id="message"></div>
    </div>

    <script>
        // Simple status update
        function updateStatus(status) {
            const statusElement = document.getElementById('status');
            if (status === 'connected') {
                statusElement.className = 'status connected';
                statusElement.innerHTML = '<h2>System Status: Connected</h2><p>RAG application is running successfully!</p>';
            } else {
                statusElement.className = 'status disconnected';
                statusElement.innerHTML = '<h2>System Status: Disconnected</h2><p>Waiting for system to start...</p>';
            }
        }

        // Simulate connection
        setTimeout(() => {
            updateStatus('connected');
        }, 1000);

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const message = document.getElementById('message');
            message.innerHTML = '<p>Click "Start Server" to begin the RAG application.</p>';
        });
    </script>
</body>
</html>
