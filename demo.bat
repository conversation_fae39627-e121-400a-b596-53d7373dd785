@echo off
echo.
echo 🚀 RAG System Demo - Zero Complex Dependencies!
echo ================================================
echo.

echo 📦 Installing ONLY the essentials...
pip install fastapi uvicorn

echo.
echo 🎉 Starting RAG System Demo Server...
echo.
echo 📍 Access Points:
echo 🌐 Interactive API Docs: http://localhost:8000/api/v1/docs
echo 🔍 Health Check: http://localhost:8000/health
echo 📊 Alternative Docs: http://localhost:8000/api/v1/redoc
echo 🏠 Welcome Page: http://localhost:8000/
echo.
echo 💡 This demo shows the complete RAG system API design
echo 💡 All endpoints are documented with full examples
echo 💡 No databases or complex setup required!
echo.
echo Press Ctrl+C to stop the server
echo ================================================
echo.

python demo_server.py

echo.
echo ✅ Demo stopped. Thanks for exploring the RAG System!
pause
