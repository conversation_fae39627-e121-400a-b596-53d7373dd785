"""
Scoring model for storing candidate evaluation results.
"""

from sqlalchemy import Column, String, Float, Integer, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class CandidateScore(Base):
    """
    Candidate scoring model for storing RAG-based evaluation results.
    """
    __tablename__ = "candidate_scores"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Associations
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("candidates.id"), nullable=False)
    job_id = Column(UUID(as_uuid=True), ForeignKey("job_postings.id"), nullable=False)
    scored_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    
    # Overall scoring
    overall_score = Column(Float, nullable=False)  # 0.0 to 1.0
    overall_percentage = Column(Integer, nullable=False)  # 0 to 100
    fit_level = Column(String(50), nullable=False)  # excellent, good, fair, poor
    
    # Detailed scoring breakdown
    requirement_scores = Column(Text)  # JSON array of RequirementScore objects
    notable_strengths = Column(Text)  # JSON array of strengths
    potential_gaps = Column(Text)  # JSON array of gaps
    
    # Evidence and reasoning
    key_evidence = Column(Text)  # JSON array of evidence snippets
    reasoning_summary = Column(Text)
    confidence_score = Column(Float)  # 0.0 to 1.0
    
    # Processing metadata
    total_evidence_chunks = Column(Integer)
    processing_time_seconds = Column(Float)
    model_used = Column(String(100))
    embedding_model = Column(String(100))
    
    # Custom weighting and configuration
    custom_requirements = Column(Text)  # JSON array of custom requirements
    weighting_preferences = Column(Text)  # JSON object of weights
    
    # Version and tracking
    score_version = Column(String(20), default="1.0")
    is_latest = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    candidate = relationship("Candidate", back_populates="scores")
    job = relationship("JobPosting", back_populates="scores")
    
    def __repr__(self):
        return f"<CandidateScore(id={self.id}, score={self.overall_score:.2f}, fit={self.fit_level})>"
    
    @property
    def score_grade(self) -> str:
        """Convert score to letter grade."""
        if self.overall_percentage >= 90:
            return "A+"
        elif self.overall_percentage >= 85:
            return "A"
        elif self.overall_percentage >= 80:
            return "B+"
        elif self.overall_percentage >= 75:
            return "B"
        elif self.overall_percentage >= 70:
            return "C+"
        elif self.overall_percentage >= 65:
            return "C"
        elif self.overall_percentage >= 60:
            return "D"
        else:
            return "F"
    
    @property
    def is_recommended(self) -> bool:
        """Determine if candidate is recommended based on score."""
        return self.overall_percentage >= 70 and self.fit_level in ["excellent", "good"]


class RequirementScore(Base):
    """
    Individual requirement scoring model for detailed breakdowns.
    """
    __tablename__ = "requirement_scores"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Associations
    candidate_score_id = Column(UUID(as_uuid=True), ForeignKey("candidate_scores.id"), nullable=False)
    
    # Requirement details
    requirement_text = Column(String(500), nullable=False)
    requirement_type = Column(String(50))  # required, preferred, nice_to_have
    requirement_category = Column(String(100))  # skills, experience, education, etc.
    
    # Scoring
    score = Column(Float, nullable=False)  # 0.0 to 1.0
    confidence = Column(Float, nullable=False)  # 0.0 to 1.0
    weight = Column(Float, default=1.0)  # Requirement weight in overall score
    
    # Evidence and explanation
    evidence_snippets = Column(Text)  # JSON array of evidence
    explanation = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<RequirementScore(id={self.id}, score={self.score:.2f}, requirement={self.requirement_text[:50]}...)>"
