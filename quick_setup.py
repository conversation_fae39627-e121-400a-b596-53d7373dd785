#!/usr/bin/env python3
"""
Quick Setup Script for RAG System
Checks environment, creates directories, and verifies configuration.
"""

import os
import sys
from pathlib import Path
import subprocess


def create_directories():
    """Create necessary directories."""
    dirs_to_create = [
        "data/chroma",
        "data/uploads",
        "logs"
    ]
    
    print("📁 Creating directories...")
    for dir_path in dirs_to_create:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ Created: {dir_path}")


def check_environment():
    """Check environment configuration."""
    print("\n🔧 Checking environment configuration...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("   ❌ .env file not found!")
        print("   📝 Please copy .env.example to .env and configure your settings")
        return False
    
    # Check for OpenAI API key
    with open(env_file, 'r') as f:
        env_content = f.read()
    
    if "your-openai-api-key-here" in env_content:
        print("   ⚠️  OpenAI API key not configured!")
        print("   📝 Please set OPENAI_API_KEY in your .env file")
        print("   💡 Get your API key from: https://platform.openai.com/api-keys")
        return False
    
    print("   ✅ Environment configuration looks good")
    return True


def check_dependencies():
    """Check if required dependencies are installed."""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'openai',
        'chromadb',
        'pydantic',
        'structlog'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - Missing!")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   💡 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True


def test_rag_services():
    """Test basic RAG services functionality."""
    print("\n🧪 Testing RAG services...")
    
    try:
        # Add current directory to Python path
        sys.path.insert(0, str(Path.cwd()))
        
        from app.core.config import get_settings
        settings = get_settings()
        print("   ✅ Configuration loading")
        
        from app.services.rag_engine import get_rag_engine
        rag_engine = get_rag_engine()
        print("   ✅ RAG engine initialization")
        
        print("   🎉 Basic services are working!")
        return True
        
    except Exception as e:
        print(f"   ❌ Service test failed: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 RAG System Quick Setup")
    print("=" * 40)
    
    # Create directories
    create_directories()
    
    # Check dependencies
    deps_ok = check_dependencies()
    if not deps_ok:
        print("\n❌ Please install missing dependencies first")
        print("Run: pip install -r requirements.txt")
        return False
    
    # Check environment
    env_ok = check_environment()
    
    # Test services
    services_ok = test_rag_services()
    
    print("\n" + "=" * 40)
    
    if env_ok and services_ok:
        print("✅ Setup completed successfully!")
        print("\n🎯 Next steps:")
        print("1. Test the pipeline: python test_rag_pipeline.py")
        print("2. Start demo server: python working_demo.py")
        print("3. Start full API: uvicorn app.main:app --reload")
        print("\n📖 API Documentation will be at: http://localhost:8000/docs")
    else:
        print("⚠️  Setup completed with warnings")
        print("Please address the issues above before running the system")
        
        if not env_ok:
            print("\n💡 Environment configuration help:")
            print("- Copy .env.example to .env")
            print("- Set your OpenAI API key")
            print("- Adjust other settings as needed")


if __name__ == "__main__":
    main()
