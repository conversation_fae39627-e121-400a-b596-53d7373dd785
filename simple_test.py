#!/usr/bin/env python3
"""
Simple test script to verify RAG system basic functionality.
"""

import os
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def test_basic_imports():
    """Test basic imports work."""
    print("Testing basic imports...")
    
    try:
        from app.core.config import get_settings
        print("  [OK] Config module")
        
        from app.services.rag_engine import get_rag_engine
        print("  [OK] RAG Engine")
        
        from app.services.vector_store import get_vector_store
        print("  [OK] Vector Store")
        
        from app.services.llm_service import get_llm_service
        print("  [OK] LLM Service")
        
        from app.services.document_processor import get_document_processor
        print("  [OK] Document Processor")
        
        return True
    except Exception as e:
        print(f"  [ERROR] Import failed: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from app.core.config import get_settings
        settings = get_settings()
        print(f"  [OK] App name: {settings.app_name}")
        print(f"  [OK] Debug mode: {settings.debug}")
        
        # Check OpenAI key
        if hasattr(settings.llm, 'openai_api_key'):
            if settings.llm.openai_api_key and settings.llm.openai_api_key != "your-openai-api-key-here":
                print("  [OK] OpenAI API key configured")
            else:
                print("  [WARNING] OpenAI API key not configured")
        
        return True
    except Exception as e:
        print(f"  [ERROR] Configuration failed: {e}")
        return False

def test_directories():
    """Test required directories exist."""
    print("\nTesting directories...")
    
    dirs_to_check = [
        "data/chroma",
        "data/uploads"
    ]
    
    all_ok = True
    for dir_path in dirs_to_check:
        if Path(dir_path).exists():
            print(f"  [OK] {dir_path}")
        else:
            print(f"  [CREATING] {dir_path}")
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            all_ok = False
    
    return all_ok

def main():
    """Main test function."""
    print("RAG System Basic Test")
    print("=" * 40)
    
    tests = [
        test_directories,
        test_basic_imports,
        test_configuration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"  [ERROR] Test failed: {e}")
            results.append(False)
    
    print("\n" + "=" * 40)
    if all(results):
        print("SUCCESS: All basic tests passed!")
        print("\nNext steps:")
        print("1. Set OPENAI_API_KEY in .env file")
        print("2. Run: python working_demo.py")
        print("3. Visit: http://localhost:8000")
    else:
        print("WARNING: Some tests failed")
        print("Please check the errors above")

if __name__ == "__main__":
    main()
