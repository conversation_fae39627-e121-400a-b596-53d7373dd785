"""
Main API router for version 1 endpoints.
Aggregates all API route modules into a single router.
"""

from fastapi import APIRouter

# Import route modules
from app.api.v1.routes import (
    auth,
    candidates,
    documents,
    jobs,
    scoring,
    outreach,
    health
)

# Create main API router
api_router = APIRouter()

# Include all route modules
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    candidates.router,
    prefix="/candidates", 
    tags=["candidates"]
)

api_router.include_router(
    documents.router,
    prefix="/documents",
    tags=["documents"]
)

api_router.include_router(
    jobs.router,
    prefix="/jobs",
    tags=["job-postings"]
)

api_router.include_router(
    scoring.router,
    prefix="/scoring",
    tags=["candidate-scoring"]
)

api_router.include_router(
    outreach.router,
    prefix="/outreach",
    tags=["email-outreach"]
)

api_router.include_router(
    health.router,
    prefix="/health",
    tags=["system-health"]
)
