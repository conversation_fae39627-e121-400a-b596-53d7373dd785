# =============================================================================
# RAG System Environment Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# Application Settings
# -----------------------------------------------------------------------------
APP_NAME=Recruitment RAG System
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# -----------------------------------------------------------------------------
# API Configuration
# -----------------------------------------------------------------------------
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# -----------------------------------------------------------------------------
# Security
# -----------------------------------------------------------------------------
SECRET_KEY=your-super-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# -----------------------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------------------
# PostgreSQL for metadata storage
DATABASE_URL=postgresql://rag_user:rag_password@localhost:5432/rag_db
DB_ECHO=false

# -----------------------------------------------------------------------------
# Vector Database Configuration
# -----------------------------------------------------------------------------
# ChromaDB configuration
CHROMA_PERSIST_DIRECTORY=./data/chroma
CHROMA_HOST=localhost
CHROMA_PORT=8001

# Alternative: Pinecone (for production scaling)
# PINECONE_API_KEY=your-pinecone-api-key
# PINECONE_ENVIRONMENT=your-pinecone-environment
# PINECONE_INDEX_NAME=recruitment-rag

# -----------------------------------------------------------------------------
# LLM Configuration
# -----------------------------------------------------------------------------
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-1106-preview
OPENAI_EMBEDDING_MODEL=text-embedding-3-large
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.1

# Alternative: Anthropic Claude
# ANTHROPIC_API_KEY=your-anthropic-api-key

# Local LLM (via Ollama)
# OLLAMA_HOST=http://localhost:11434
# OLLAMA_MODEL=llama3.1:7b

# -----------------------------------------------------------------------------
# Redis Configuration (for Celery)
# -----------------------------------------------------------------------------
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# -----------------------------------------------------------------------------
# File Storage Configuration
# -----------------------------------------------------------------------------
UPLOAD_DIR=./data/uploads
MAX_FILE_SIZE_MB=50
ALLOWED_EXTENSIONS=pdf,docx,doc,txt,md,html

# -----------------------------------------------------------------------------
# Processing Configuration
# -----------------------------------------------------------------------------
# Embedding Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_CHUNKS_PER_DOCUMENT=100

# RAG Configuration
TOP_K_RETRIEVAL=5
SIMILARITY_THRESHOLD=0.7
MAX_CONTEXT_LENGTH=8000

# -----------------------------------------------------------------------------
# External APIs
# -----------------------------------------------------------------------------
# LinkedIn API (if integrating)
# LINKEDIN_CLIENT_ID=your-linkedin-client-id
# LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# GitHub API (for repo analysis)
# GITHUB_TOKEN=your-github-personal-access-token

# -----------------------------------------------------------------------------
# Monitoring & Logging
# -----------------------------------------------------------------------------
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_METRICS=true
METRICS_PORT=9090

# Sentry (for error tracking)
# SENTRY_DSN=your-sentry-dsn

# -----------------------------------------------------------------------------
# Email Configuration (for outreach testing)
# -----------------------------------------------------------------------------
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# EMAIL_FROM=<EMAIL>

# -----------------------------------------------------------------------------
# Rate Limiting
# -----------------------------------------------------------------------------
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# -----------------------------------------------------------------------------
# Cache Configuration
# -----------------------------------------------------------------------------
CACHE_TTL_SECONDS=3600
ENABLE_CACHE=true
