# =============================================================================
# RAG System - Core Dependencies (Windows Compatible)
# =============================================================================

# Core FastAPI Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Database & ORM
sqlalchemy==2.0.23
alembic==1.13.1

# Vector Database & AI
chromadb==0.4.18
openai==1.3.7

# Document Processing (Windows-friendly alternatives)
python-docx==1.0.1
python-multipart==0.0.6

# Background Task Processing
celery==5.3.4
redis==5.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# HTTP Client
httpx==0.25.2
requests==2.31.0

# Logging & Monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Configuration & Environment
python-dotenv==1.0.0
click==8.1.7

# Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1

# Optional: PostgreSQL driver (if needed)
# psycopg2-binary==2.9.9

# Optional: Advanced PDF processing (requires Visual Studio)
# PyMuPDF==1.23.8
# pdfplumber==0.10.3
