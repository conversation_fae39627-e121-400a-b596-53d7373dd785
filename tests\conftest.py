"""
Test configuration and fixtures for the RAG system.
"""

import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from fastapi.testclient import TestClient
import tempfile
import shutil
from pathlib import Path

from app.main import app
from app.db.database import get_db, Base
from app.core.config import get_settings


# =============================================================================
# Test Database Setup
# =============================================================================

@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def test_engine():
    """Create test database engine."""
    # Use in-memory SQLite for tests
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=False,
        connect_args={"check_same_thread": False}
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()


@pytest_asyncio.fixture
async def test_db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async_session_maker = async_sessionmaker(
        test_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    async with async_session_maker() as session:
        yield session


@pytest.fixture
def test_client(test_db_session) -> TestClient:
    """Create test client with database override."""
    
    def override_get_db():
        return test_db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as client:
        yield client
    
    app.dependency_overrides.clear()


# =============================================================================
# Test Data Fixtures
# =============================================================================

@pytest.fixture
def sample_candidate_data():
    """Sample candidate data for testing."""
    return {
        "email": "<EMAIL>",
        "full_name": "John Doe",
        "phone": "******-0123",
        "linkedin_url": "https://linkedin.com/in/johndoe",
        "github_url": "https://github.com/johndoe",
        "current_location": "San Francisco, CA",
        "years_experience": 5,
        "current_role": "Senior Software Engineer",
        "skills": ["Python", "React", "PostgreSQL", "Docker"],
        "notes": "Excellent candidate with strong technical background"
    }


@pytest.fixture
def sample_job_data():
    """Sample job posting data for testing."""
    return {
        "title": "Senior Python Developer",
        "company": "TechCorp Inc",
        "location": "San Francisco, CA",
        "remote_allowed": True,
        "job_type": "full-time",
        "experience_level": "senior",
        "description": "We are looking for a Senior Python Developer to join our team...",
        "requirements": [
            "5+ years Python experience",
            "Experience with FastAPI or Django",
            "PostgreSQL knowledge",
            "Docker experience"
        ],
        "nice_to_have": [
            "React experience",
            "AWS knowledge",
            "Machine learning background"
        ],
        "salary_range_min": 120000,
        "salary_range_max": 180000
    }


@pytest.fixture
def sample_pdf_content():
    """Sample PDF content for testing document processing."""
    # This would be actual PDF bytes in a real test
    return b"Sample PDF content for testing"


@pytest.fixture
def temp_upload_dir():
    """Create temporary upload directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


# =============================================================================
# Mock Service Fixtures
# =============================================================================

@pytest.fixture
def mock_openai_response():
    """Mock OpenAI API response."""
    return {
        "text": '{"candidate_name": "John Doe", "overall_score": 0.85, "overall_percentage": 85, "fit_level": "excellent", "requirements": [], "notable_strengths": ["Strong Python skills"], "potential_gaps": [], "reasoning_summary": "Excellent match"}',
        "model": "gpt-4-1106-preview",
        "usage": {
            "input_tokens": 500,
            "output_tokens": 200,
            "total_tokens": 700
        },
        "generation_time_seconds": 2.5,
        "finish_reason": "stop"
    }


@pytest.fixture
def mock_embedding_response():
    """Mock embedding API response."""
    return [[0.1, 0.2, 0.3] * 512]  # Mock 1536-dimensional embedding


@pytest.fixture
def mock_vector_search_results():
    """Mock vector search results."""
    return [
        {
            "id": "doc_1",
            "content": "John has 5 years of Python development experience",
            "metadata": {"document_type": "resume", "candidate_id": "123"},
            "similarity_score": 0.92,
            "distance": 0.08
        },
        {
            "id": "doc_2", 
            "content": "Built scalable web applications using FastAPI",
            "metadata": {"document_type": "resume", "candidate_id": "123"},
            "similarity_score": 0.88,
            "distance": 0.12
        }
    ]


# =============================================================================
# Test Settings Override
# =============================================================================

@pytest.fixture
def test_settings():
    """Override settings for testing."""
    settings = get_settings()
    
    # Override with test values
    settings.database.url = "sqlite+aiosqlite:///:memory:"
    settings.file_storage.upload_dir = "/tmp/test_uploads"
    settings.vectordb.chroma_persist_directory = "/tmp/test_chroma"
    settings.llm.openai_api_key = "test-api-key"
    
    return settings


# =============================================================================
# Async Test Utilities
# =============================================================================

class AsyncMock:
    """Simple async mock for testing async functions."""
    
    def __init__(self, return_value=None):
        self.return_value = return_value
        self.call_count = 0
        self.call_args_list = []
    
    async def __call__(self, *args, **kwargs):
        self.call_count += 1
        self.call_args_list.append((args, kwargs))
        return self.return_value


# =============================================================================
# Pytest Configuration
# =============================================================================

def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "slow: marks tests as slow running"
    )
    config.addinivalue_line(
        "markers", "external: marks tests that require external services"
    )
