"""
Structured logging configuration for the RAG system.
Uses structlog for consistent, searchable log output.
"""

import logging
import logging.config
import sys
from typing import Any, Dict
import structlog
from structlog.stdlib import LoggerFactory


def setup_logging(level: str = "INFO", format_type: str = "json") -> None:
    """
    Configure structured logging for the application.
    
    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_type: Format type ("json" or "console")
    """
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, level.upper()),
    )
    
    # Configure structlog
    processors = [
        # Add caller info (file, line, function)
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        
        # Add timestamp
        structlog.processors.TimeStamper(fmt="ISO"),
        
        # Add process info for debugging
        structlog.processors.add_log_level,
        
        # Custom processors for RAG context
        add_request_context,
    ]
    
    if format_type.lower() == "json":
        # JSON output for production
        processors.append(structlog.processors.JSONRenderer())
    else:
        # Human-readable console output for development
        processors.extend([
            structlog.dev.ConsoleRenderer(colors=True),
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def add_request_context(logger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add request context to log entries when available.
    
    Args:
        logger: Logger instance
        method_name: Log method name
        event_dict: Log event dictionary
        
    Returns:
        Enhanced event dictionary with context
    """
    # This will be enhanced when we have request context
    # For now, just pass through
    return event_dict


class StructuredLogger:
    """
    Wrapper class for structured logging with RAG-specific methods.
    """
    
    def __init__(self, name: str):
        self.logger = structlog.get_logger(name)
    
    def log_document_processing(
        self,
        filename: str,
        status: str,
        chunks_created: int = 0,
        processing_time: float = 0.0,
        error: str = None
    ) -> None:
        """Log document processing events."""
        event_data = {
            "event_type": "document_processing",
            "filename": filename,
            "status": status,
            "chunks_created": chunks_created,
            "processing_time_seconds": processing_time
        }
        
        if error:
            event_data["error"] = error
            self.logger.error("Document processing failed", **event_data)
        else:
            self.logger.info("Document processing completed", **event_data)
    
    def log_embedding_operation(
        self,
        operation: str,
        text_length: int,
        embedding_dimensions: int = 0,
        processing_time: float = 0.0,
        provider: str = "openai",
        error: str = None
    ) -> None:
        """Log embedding generation events."""
        event_data = {
            "event_type": "embedding_operation",
            "operation": operation,
            "text_length": text_length,
            "embedding_dimensions": embedding_dimensions,
            "processing_time_seconds": processing_time,
            "provider": provider
        }
        
        if error:
            event_data["error"] = error
            self.logger.error("Embedding operation failed", **event_data)
        else:
            self.logger.info("Embedding operation completed", **event_data)
    
    def log_vector_search(
        self,
        query: str,
        results_count: int,
        search_time: float = 0.0,
        similarity_threshold: float = 0.0,
        error: str = None
    ) -> None:
        """Log vector search events."""
        event_data = {
            "event_type": "vector_search",
            "query_length": len(query),
            "results_count": results_count,
            "search_time_seconds": search_time,
            "similarity_threshold": similarity_threshold
        }
        
        if error:
            event_data["error"] = error
            self.logger.error("Vector search failed", **event_data)
        else:
            self.logger.info("Vector search completed", **event_data)
    
    def log_llm_call(
        self,
        prompt_type: str,
        input_tokens: int,
        output_tokens: int,
        model: str,
        response_time: float = 0.0,
        error: str = None
    ) -> None:
        """Log LLM API calls."""
        event_data = {
            "event_type": "llm_call",
            "prompt_type": prompt_type,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "total_tokens": input_tokens + output_tokens,
            "model": model,
            "response_time_seconds": response_time
        }
        
        if error:
            event_data["error"] = error
            self.logger.error("LLM call failed", **event_data)
        else:
            self.logger.info("LLM call completed", **event_data)
    
    def log_candidate_scoring(
        self,
        candidate_id: str,
        job_id: str,
        overall_score: float,
        evidence_count: int,
        processing_time: float = 0.0,
        error: str = None
    ) -> None:
        """Log candidate scoring events."""
        event_data = {
            "event_type": "candidate_scoring",
            "candidate_id": candidate_id,
            "job_id": job_id,
            "overall_score": overall_score,
            "evidence_count": evidence_count,
            "processing_time_seconds": processing_time
        }
        
        if error:
            event_data["error"] = error
            self.logger.error("Candidate scoring failed", **event_data)
        else:
            self.logger.info("Candidate scoring completed", **event_data)
    
    def log_email_generation(
        self,
        candidate_id: str,
        email_length: int,
        personalization_points: int,
        generation_time: float = 0.0,
        error: str = None
    ) -> None:
        """Log outreach email generation events."""
        event_data = {
            "event_type": "email_generation",
            "candidate_id": candidate_id,
            "email_length": email_length,
            "personalization_points": personalization_points,
            "generation_time_seconds": generation_time
        }
        
        if error:
            event_data["error"] = error
            self.logger.error("Email generation failed", **event_data)
        else:
            self.logger.info("Email generation completed", **event_data)


def get_rag_logger(name: str) -> StructuredLogger:
    """
    Get a RAG-specific structured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        StructuredLogger instance
    """
    return StructuredLogger(name)
