# =============================================================================
# Pytest Configuration
# =============================================================================

[tool:pytest]
minversion = 6.0
addopts = 
    -ra 
    -q 
    --strict-markers
    --disable-warnings
    --tb=short
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    external: Tests requiring external services (OpenAI, etc.)
    
# Async test configuration
asyncio_mode = auto

# Coverage configuration
--cov = app
--cov-report = html:htmlcov
--cov-report = term-missing
--cov-fail-under = 80

# Parallel testing
-n = auto

[coverage:run]
source = app
omit = 
    app/tests/*
    app/migrations/*
    */venv/*
    */virtualenv/*
    */.venv/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\(Protocol\):
    @(abc\.)?abstractmethod
