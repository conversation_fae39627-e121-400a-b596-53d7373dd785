# =============================================================================
# Docker Compose for RAG System - Production Ready
# Includes database, authentication, monitoring, and background processing
# =============================================================================

version: '3.8'

services:
  # -----------------------------------------------------------------------------
  # PostgreSQL Database
  # -----------------------------------------------------------------------------
  postgres:
    image: postgres:15-alpine
    container_name: rag-postgres
    environment:
      POSTGRES_DB: rag_db
      POSTGRES_USER: rag_user
      POSTGRES_PASSWORD: rag_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rag_user -d rag_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # Redis (for Celery task queue and caching)
  # -----------------------------------------------------------------------------
  redis:
    image: redis:7-alpine
    container_name: rag-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # ChromaDB (Vector Database)
  # -----------------------------------------------------------------------------
  chroma:
    image: ghcr.io/chroma-core/chroma:latest
    container_name: rag-chroma
    environment:
      CHROMA_SERVER_HOST: 0.0.0.0
      CHROMA_SERVER_HTTP_PORT: 8001
      CHROMA_SERVER_CORS_ALLOW_ORIGINS: '["*"]'
    ports:
      - "8001:8001"
    volumes:
      - chroma_data:/chroma/chroma
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # Main API Application (Production Ready)
  # -----------------------------------------------------------------------------
  api:
    build:
      context: .
      target: production
    container_name: rag-api
    environment:
      # Database
      DATABASE_URL: ************************************************/rag_db
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      
      # ChromaDB
      CHROMA_HOST: chroma
      CHROMA_PORT: 8001
      CHROMA_PERSIST_DIRECTORY: /app/data/chroma
      
      # OpenAI (set in .env file)
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      OPENAI_MODEL: gpt-4-1106-preview
      OPENAI_EMBEDDING_MODEL: text-embedding-3-large
      
      # Security
      SECRET_KEY: ${SECRET_KEY:-prod-secret-key-change-this}
      ACCESS_TOKEN_EXPIRE_MINUTES: 30
      
      # App settings
      DEBUG: false
      ENVIRONMENT: production
      
      # Logging & Monitoring
      LOG_LEVEL: INFO
      LOG_FORMAT: json
      ENABLE_METRICS: true
      
      # File storage
      UPLOAD_DIR: /app/data/uploads
      MAX_FILE_SIZE_MB: 50
    ports:
      - "8000:8000"
    volumes:
      - api_data:/app/data
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      chroma:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # Celery Worker (Background Processing)
  # -----------------------------------------------------------------------------
  worker:
    build:
      context: .
      target: production
    container_name: rag-worker
    environment:
      # Database
      DATABASE_URL: ************************************************/rag_db
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      
      # ChromaDB
      CHROMA_HOST: chroma
      CHROMA_PORT: 8001
      
      # OpenAI
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      
      # App settings
      ENVIRONMENT: production
      LOG_LEVEL: INFO
      
      # Worker specific
      WORKER_CONCURRENCY: 2
      WORKER_PREFETCH_MULTIPLIER: 1
    volumes:
      - api_data:/app/data
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
      - chroma
    command: celery -A app.workers.celery_app worker --loglevel=info --concurrency=2
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # Celery Beat (Periodic Tasks)
  # -----------------------------------------------------------------------------
  beat:
    build:
      context: .
      target: production
    container_name: rag-beat
    environment:
      # Database
      DATABASE_URL: ************************************************/rag_db
      
      # Redis
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      
      # App settings
      ENVIRONMENT: production
      LOG_LEVEL: INFO
    volumes:
      - api_data:/app/data
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    command: celery -A app.workers.celery_app beat --loglevel=info
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # Celery Flower (Task Monitoring)
  # -----------------------------------------------------------------------------
  flower:
    build:
      context: .
      target: production
    container_name: rag-flower
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      FLOWER_PORT: 5555
      FLOWER_BASIC_AUTH: ${FLOWER_USER:-admin}:${FLOWER_PASSWORD:-admin}
    ports:
      - "5555:5555"
    depends_on:
      - redis
    command: celery -A app.workers.celery_app flower --port=5555
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # Prometheus (Metrics Collection)
  # -----------------------------------------------------------------------------
  prometheus:
    image: prom/prometheus:latest
    container_name: rag-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=30d'
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # Grafana (Metrics Visualization)
  # -----------------------------------------------------------------------------
  grafana:
    image: grafana/grafana:latest
    container_name: rag-grafana
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
      - ./config/grafana/dashboards.yml:/etc/grafana/provisioning/dashboards/dashboards.yml
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - rag-network

  # -----------------------------------------------------------------------------
  # Nginx (Reverse Proxy & Load Balancer)
  # -----------------------------------------------------------------------------
  nginx:
    image: nginx:alpine
    container_name: rag-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - rag-network

# =============================================================================
# Volumes
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  chroma_data:
    driver: local
  api_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local

# =============================================================================
# Networks
# =============================================================================
networks:
  rag-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
