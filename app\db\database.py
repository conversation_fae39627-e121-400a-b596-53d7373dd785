"""
Database configuration and connection management.
Handles SQLAlchemy setup, connection pooling, and database initialization.
"""

import asyncio
from typing import Async<PERSON>enerator
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    create_async_engine,
    async_sessionmaker,
)
from sqlalchemy.orm import DeclarativeBase, declared_attr
from sqlalchemy.pool import St<PERSON><PERSON>ool
import structlog

from app.core.config import get_settings

logger = structlog.get_logger(__name__)

# Global variables for database components
engine = None
async_session_maker = None


class Base(DeclarativeBase):
    """
    Base class for all database models.
    Provides common functionality and naming conventions.
    """
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        return cls.__name__.lower()


async def init_db() -> None:
    """
    Initialize database connection and create tables.
    Called during application startup.
    """
    global engine, async_session_maker
    
    settings = get_settings()
    
    # Create async engine with connection pooling
    engine = create_async_engine(
        settings.database.url.replace("postgresql://", "postgresql+asyncpg://"),
        echo=settings.database.echo,
        pool_size=settings.database.pool_size,
        max_overflow=settings.database.max_overflow,
        pool_pre_ping=True,  # Validate connections before use
        connect_args={
            "server_settings": {
                "application_name": settings.app_name,
            }
        } if "postgresql" in settings.database.url else {}
    )
    
    # Create session maker
    async_session_maker = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    # Import all models to ensure they're registered
    from app.models import (
        user,
        candidate,
        job_posting,
        document,
        scoring,
        email_template
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("Database initialized successfully")


async def close_db() -> None:
    """
    Close database connections.
    Called during application shutdown.
    """
    global engine
    
    if engine:
        await engine.dispose()
        logger.info("Database connections closed")


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.
    
    Yields:
        AsyncSession: Database session instance
    """
    if not async_session_maker:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


class DatabaseManager:
    """
    Database manager class for advanced operations.
    Provides utilities for transactions, bulk operations, etc.
    """
    
    def __init__(self):
        self.engine = engine
        self.async_session_maker = async_session_maker
    
    async def execute_transaction(self, operations):
        """
        Execute multiple operations in a single transaction.
        
        Args:
            operations: List of async functions to execute
        """
        async with self.async_session_maker() as session:
            try:
                for operation in operations:
                    await operation(session)
                await session.commit()
            except Exception:
                await session.rollback()
                raise
    
    async def bulk_insert(self, model_class, data_list):
        """
        Perform bulk insert operation.
        
        Args:
            model_class: SQLAlchemy model class
            data_list: List of dictionaries with model data
        """
        async with self.async_session_maker() as session:
            try:
                objects = [model_class(**data) for data in data_list]
                session.add_all(objects)
                await session.commit()
                return objects
            except Exception:
                await session.rollback()
                raise
    
    async def health_check(self) -> bool:
        """
        Check database health.
        
        Returns:
            bool: True if database is healthy
        """
        try:
            async with self.async_session_maker() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False


def get_db_manager() -> DatabaseManager:
    """
    Get database manager instance.
    
    Returns:
        DatabaseManager: Database manager instance
    """
    return DatabaseManager()
