#!/usr/bin/env python3
"""
LM Studio Integration Demo for RAG System

This demo shows how to easily switch between OpenAI and LM Studio
in your production RAG system.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from app.core.config import get_settings


def print_banner():
    """Print demo banner."""
    print("""
🚀 RAG System - LM Studio Integration Demo
=========================================

Your RAG system now supports multiple AI providers:

🌐 OpenAI GPT Models (Cloud)
   • GPT-4 Turbo for high-quality responses
   • text-embedding-3-large for embeddings
   • Best for production with budget

🏠 LM Studio (Local)
   • Run models locally on your hardware
   • Complete privacy - no data leaves your system
   • No API costs - unlimited usage
   • Great for development and privacy-sensitive use cases

🔄 Easy Provider Switching
   • Change one configuration setting
   • Same API, different backend
   • Seamless integration
""")


def show_current_config():
    """Show current LLM configuration."""
    try:
        settings = get_settings()
        
        print("📋 Current Configuration:")
        print(f"   Provider: {settings.llm.llm_provider}")
        
        if settings.llm.llm_provider == "openai":
            has_key = settings.llm.openai_api_key and settings.llm.openai_api_key != "your-openai-api-key-here"
            print(f"   OpenAI API Key: {'✅ Configured' if has_key else '❌ Not Set'}")
            print(f"   OpenAI Model: {settings.llm.openai_model}")
            
        elif settings.llm.llm_provider == "lmstudio":
            print(f"   LM Studio Host: {settings.llm.lmstudio_host}")
            print(f"   LM Studio Model: {settings.llm.lmstudio_model}")
        
        print()
        
    except Exception as e:
        print(f"❌ Configuration error: {str(e)}")


def show_lmstudio_setup():
    """Show LM Studio setup instructions."""
    print("""
🏠 LM Studio Setup Guide:
=========================

1. 📥 Download LM Studio
   • Visit: https://lmstudio.ai/
   • Download for your OS (Windows/Mac/Linux)
   • Install and launch

2. 🤖 Download a Model
   • Click "Discover" in LM Studio
   • Recommended models:
     * Meta Llama 3.1 8B (good balance)
     * Mistral 7B (fast and efficient)
     * Code Llama 13B (for code tasks)
   • Choose size based on your RAM:
     * 7-8B models: 8GB+ RAM
     * 13B models: 16GB+ RAM
     * 34B+ models: 32GB+ RAM

3. 🔧 Start the Server
   • Go to "Server" tab in LM Studio
   • Click "Start Server"
   • Default: http://localhost:1234
   • Note the model name shown

4. ⚙️ Update RAG Configuration
   • Edit your .env file:
     LLM_PROVIDER=lmstudio
     LLM_LMSTUDIO_HOST=http://localhost:1234
     LLM_LMSTUDIO_MODEL=your-model-name
   • Restart your RAG system

5. 🧪 Test Integration
   • Run: python test_lmstudio.py
   • Or start your RAG system normally
""")


def show_provider_comparison():
    """Show comparison between providers."""
    print("""
🔍 Provider Comparison:
======================

OpenAI (Cloud)                  LM Studio (Local)
--------------                  ------------------
✅ High quality responses       ✅ Complete privacy
✅ Latest GPT-4 models          ✅ No API costs
✅ No local hardware needed     ✅ Unlimited usage
✅ Always up-to-date            ✅ Works offline
❌ Requires API key & costs     ❌ Requires powerful hardware
❌ Data sent to OpenAI          ❌ Model setup required
❌ Usage limits                 ❌ Slower than cloud
❌ Internet required            ❌ Limited model selection

Best For:                       Best For:
• Production deployments       • Development/testing
• High-quality requirements    • Privacy-sensitive data
• Teams without ML hardware    • Cost-conscious projects
• Rapid prototyping           • Offline environments
""")


def show_switching_guide():
    """Show how to switch between providers."""
    print("""
🔄 How to Switch Providers:
===========================

Method 1: Environment Variable
------------------------------
# Switch to LM Studio
LLM_PROVIDER=lmstudio

# Switch to OpenAI  
LLM_PROVIDER=openai

Method 2: In Code (for testing)
-------------------------------
from app.core.config import get_settings

# Temporarily switch provider
settings = get_settings()
settings.llm.llm_provider = "lmstudio"  # or "openai"

Method 3: Runtime Configuration
-------------------------------
Your RAG system automatically detects the provider
and switches the backend while keeping the same API.

🔄 No code changes needed in your application!
""")


def show_usage_examples():
    """Show usage examples."""
    print("""
💡 Usage Examples:
=================

# Your existing RAG code works with both providers:

from app.services.llm_service import get_llm_service

# This works with OpenAI or LM Studio automatically
llm_service = get_llm_service()

# Generate candidate scores (same API, different backend)
result = await llm_service.generate_candidate_score(
    job_description="Senior Python Developer...",
    candidate_data="John Smith, 5 years Python...",
    evidence_snippets=[...]
)

# Generate outreach emails (same API, different backend)
email = await llm_service.generate_outreach_email(
    candidate_name="John Smith",
    company_name="TechCorp",
    role_title="Senior Python Developer",
    candidate_evidence=[...]
)

🎯 The beauty: Same code, different AI provider!
""")


async def test_quick_integration():
    """Quick integration test."""
    print("🧪 Quick Integration Test:")
    print("=========================")
    
    try:
        from app.services.llm_service import get_llm_service
        
        llm_service = get_llm_service()
        settings = get_settings()
        
        print(f"✅ LLM Service initialized with {settings.llm.llm_provider} provider")
        
        # Quick health check
        print("🔄 Testing provider health...")
        is_healthy = await llm_service.health_check()
        
        if is_healthy:
            print("✅ Provider is healthy and ready!")
            
            # Quick text generation test
            print("🔄 Testing text generation...")
            result = await llm_service.provider.generate_text(
                prompt="Hello! Please respond with 'Integration successful!' if you can read this.",
                max_tokens=50
            )
            
            print(f"✅ Response: {result['text'].strip()}")
            print(f"⏱️  Response time: {result['generation_time_seconds']:.2f}s")
            
        else:
            print("❌ Provider health check failed")
            print("💡 Make sure your provider is properly configured:")
            
            if settings.llm.llm_provider == "openai":
                print("   • Set valid OpenAI API key")
                print("   • Check internet connection")
            elif settings.llm.llm_provider == "lmstudio":
                print("   • Start LM Studio server")
                print("   • Load a model in LM Studio")
                print("   • Check LM Studio is running on correct port")
        
    except Exception as e:
        print(f"❌ Integration test failed: {str(e)}")
        print("💡 Run 'python test_lmstudio.py' for detailed diagnostics")


def main():
    """Main demo function."""
    print_banner()
    show_current_config()
    
    print("Choose an option:")
    print("1. 🔍 Show provider comparison")
    print("2. 🏠 Show LM Studio setup guide")
    print("3. 🔄 Show provider switching guide")
    print("4. 💡 Show usage examples")
    print("5. 🧪 Test current integration")
    print("0. ❌ Exit")
    
    try:
        choice = input("\nEnter your choice (0-5): ").strip()
        
        if choice == "1":
            show_provider_comparison()
        elif choice == "2":
            show_lmstudio_setup()
        elif choice == "3":
            show_switching_guide()
        elif choice == "4":
            show_usage_examples()
        elif choice == "5":
            print("Running integration test...")
            asyncio.run(test_quick_integration())
        elif choice == "0":
            print("👋 Goodbye!")
            return
        else:
            print("❌ Invalid choice")
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    main()
