"""
Email template and outreach models for personalized recruitment emails.
"""

from sqlalchemy import Column, <PERSON>, Integer, Boolean, DateTime, Text, ForeignKey, Float
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class EmailTemplate(Base):
    """
    Email template model for storing reusable email templates.
    """
    __tablename__ = "email_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Template information
    name = Column(String(255), nullable=False)
    description = Column(Text)
    template_type = Column(String(50), nullable=False)  # outreach, follow_up, rejection, offer
    
    # Template content
    subject_template = Column(Text, nullable=False)
    body_template = Column(Text, nullable=False)
    
    # Template configuration
    variables = Column(ARRAY(String))  # Available template variables
    tone = Column(String(50), default="professional")
    target_audience = Column(String(100))  # junior, senior, executive, etc.
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    success_rate = Column(Float)  # Response rate when used
    
    # Status and metadata
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    outreach_emails = relationship("OutreachEmail", back_populates="template")
    
    def __repr__(self):
        return f"<EmailTemplate(id={self.id}, name={self.name}, type={self.template_type})>"


class OutreachEmail(Base):
    """
    Outreach email model for storing generated recruitment emails.
    """
    __tablename__ = "outreach_emails"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Associations
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("candidates.id"), nullable=False)
    job_id = Column(UUID(as_uuid=True), ForeignKey("job_postings.id"), nullable=False)
    template_id = Column(UUID(as_uuid=True), ForeignKey("email_templates.id"))
    generated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    
    # Email content
    subject_line = Column(String(500), nullable=False)
    email_body = Column(Text, nullable=False)
    call_to_action = Column(String(500))
    
    # Personalization details
    personalization_points = Column(Text)  # JSON array of personalization elements
    evidence_used = Column(Text)  # JSON array of evidence snippets
    tone_analysis = Column(Text)  # JSON object with tone scores
    
    # Email metadata
    word_count = Column(Integer)
    estimated_read_time_minutes = Column(Float)
    personalization_score = Column(Float)  # 0.0 to 1.0
    
    # Generation metadata
    generation_time_seconds = Column(Float)
    model_used = Column(String(100))
    prompt_version = Column(String(20))
    
    # Email status and tracking
    status = Column(String(50), default="generated")  # generated, reviewed, sent, scheduled
    is_approved = Column(Boolean, default=False)
    
    # Send information (if applicable)
    sent_at = Column(DateTime(timezone=True))
    recipient_email = Column(String(255))
    sender_email = Column(String(255))
    message_id = Column(String(255))  # Email service message ID
    
    # Response tracking
    opened_at = Column(DateTime(timezone=True))
    clicked_at = Column(DateTime(timezone=True))
    replied_at = Column(DateTime(timezone=True))
    response_type = Column(String(50))  # positive, negative, neutral, no_response
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    candidate = relationship("Candidate", back_populates="outreach_emails")
    job = relationship("JobPosting", back_populates="outreach_emails")
    template = relationship("EmailTemplate", back_populates="outreach_emails")
    
    def __repr__(self):
        return f"<OutreachEmail(id={self.id}, candidate_id={self.candidate_id}, status={self.status})>"
    
    @property
    def is_sent(self) -> bool:
        """Check if email has been sent."""
        return self.status == "sent" and self.sent_at is not None
    
    @property
    def response_rate_score(self) -> str:
        """Get response quality score."""
        if self.response_type == "positive":
            return "High"
        elif self.response_type == "neutral":
            return "Medium"
        elif self.response_type == "negative":
            return "Low"
        else:
            return "No Response"


class EmailAnalytics(Base):
    """
    Email analytics model for tracking email performance metrics.
    """
    __tablename__ = "email_analytics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Time period
    date = Column(DateTime(timezone=True), nullable=False)
    period_type = Column(String(20), default="daily")  # daily, weekly, monthly
    
    # Volume metrics
    emails_generated = Column(Integer, default=0)
    emails_sent = Column(Integer, default=0)
    emails_opened = Column(Integer, default=0)
    emails_clicked = Column(Integer, default=0)
    emails_replied = Column(Integer, default=0)
    
    # Quality metrics
    average_personalization_score = Column(Float)
    average_generation_time = Column(Float)
    positive_responses = Column(Integer, default=0)
    negative_responses = Column(Integer, default=0)
    
    # Template performance
    top_performing_template = Column(UUID(as_uuid=True))
    template_performance = Column(Text)  # JSON object with template metrics
    
    # User and company metrics
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    company = Column(String(255))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<EmailAnalytics(id={self.id}, date={self.date}, emails_sent={self.emails_sent})>"
    
    @property
    def open_rate(self) -> float:
        """Calculate email open rate."""
        if self.emails_sent > 0:
            return (self.emails_opened / self.emails_sent) * 100
        return 0.0
    
    @property
    def response_rate(self) -> float:
        """Calculate email response rate."""
        if self.emails_sent > 0:
            return (self.emails_replied / self.emails_sent) * 100
        return 0.0
