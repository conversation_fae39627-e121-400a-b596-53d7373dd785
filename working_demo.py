#!/usr/bin/env python3
"""
Working RAG Demo Server - Full Implementation
Demonstrates the complete RAG pipeline with actual working endpoints.
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import structlog

# Import our working RAG components
from app.services.rag_engine import get_rag_engine
from app.core.config import get_settings
from app.core.logging import setup_logging

# Setup logging
setup_logging(level="INFO", format_type="console")
logger = structlog.get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="🚀 RAG System Demo - WORKING IMPLEMENTATION",
    version="1.0.0",
    description="""
    **Live RAG System Demo with Full Pipeline**
    
    ✅ **Working Features:**
    - Document upload and processing (PDF, DOCX, TXT)
    - Vector embedding generation and storage
    - Semantic candidate scoring against job descriptions
    - Personalized outreach email generation
    - Real-time evidence retrieval
    
    🏗️ **Architecture:**
    - FastAPI + Async processing
    - ChromaDB vector database
    - OpenAI GPT-4 + Embeddings
    - Structured logging
    
    📝 **Quick Test:**
    1. Upload a resume using `/upload-resume`
    2. Score the candidate using `/score-candidate`
    3. Generate email using `/generate-email`
    """,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ScoringRequest(BaseModel):
    candidate_id: str
    job_description: str
    ideal_candidate_profile: Optional[str] = None

class EmailRequest(BaseModel):
    candidate_id: str
    company_name: str
    role_title: str
    recruiter_name: Optional[str] = "Hiring Team"
    tone: str = "professional"

# Global storage for demo candidates
demo_candidates: Dict[str, Dict[str, Any]] = {}


@app.get("/", response_class=HTMLResponse)
async def demo_homepage():
    """Demo homepage with testing interface."""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>RAG System Demo</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .endpoint { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
            .method { color: #fff; padding: 4px 8px; border-radius: 4px; font-weight: bold; }
            .post { background: #49cc90; }
            .get { background: #61affe; }
            code { background: #f0f0f0; padding: 2px 4px; border-radius: 4px; }
            .example { background: #fff; border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 RAG System Demo - WORKING!</h1>
            <p>This is a fully functional RAG (Retrieval-Augmented Generation) system for recruitment.</p>
            
            <h2>🎯 Quick Start Test</h2>
            <div class="example">
                <h3>1. Upload a Resume</h3>
                <p><span class="method post">POST</span> <code>/upload-resume</code></p>
                <p>Upload a PDF, DOCX, or TXT resume file. The system will process it and store embeddings.</p>
            </div>
            
            <div class="example">
                <h3>2. Score Against Job</h3>
                <p><span class="method post">POST</span> <code>/score-candidate</code></p>
                <p>Score the candidate against a job description using semantic analysis.</p>
            </div>
            
            <div class="example">
                <h3>3. Generate Outreach Email</h3>
                <p><span class="method post">POST</span> <code>/generate-email</code></p>
                <p>Generate a personalized recruitment email based on candidate evidence.</p>
            </div>
            
            <h2>📖 API Documentation</h2>
            <p>
                <a href="/docs" target="_blank">Swagger UI Documentation</a> | 
                <a href="/redoc" target="_blank">ReDoc Documentation</a>
            </p>
            
            <h2>🧪 Test Endpoints</h2>
            <p><a href="/health">System Health Check</a></p>
            <p><a href="/demo-candidates">View Demo Candidates</a></p>
        </div>
    </body>
    </html>
    """


@app.get("/health")
async def health_check():
    """Comprehensive health check of all RAG components."""
    try:
        logger.info("Health check requested")
        rag_engine = get_rag_engine()
        health_status = await rag_engine.health_check()
        
        return {
            "status": "healthy",
            "message": "RAG System is operational",
            "timestamp": health_status["timestamp"],
            "components": health_status["components"],
            "overall_health": health_status["rag_engine"]
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@app.post("/upload-resume")
async def upload_resume(
    file: UploadFile = File(...),
    candidate_name: str = Form("Unknown Candidate"),
    document_type: str = Form("resume")
):
    """
    Upload and process a candidate resume.
    
    The system will:
    1. Validate and extract text from the document
    2. Chunk the text for optimal embedding
    3. Generate embeddings using OpenAI
    4. Store in ChromaDB vector database
    """
    try:
        logger.info("Resume upload started", filename=file.filename, candidate_name=candidate_name)
        
        # Read file content
        file_content = await file.read()
        
        if not file_content:
            raise HTTPException(status_code=400, detail="Empty file uploaded")
        
        # Create candidate ID
        candidate_id = f"candidate-{candidate_name.lower().replace(' ', '-')}-{len(demo_candidates)}"
        
        # Get RAG engine and process document
        rag_engine = get_rag_engine()
        
        documents_data = [{
            "file_content": file_content,
            "filename": file.filename,
            "document_type": document_type
        }]
        
        result = await rag_engine.process_and_store_candidate_documents(
            candidate_id=candidate_id,
            documents=documents_data
        )
        
        # Store candidate info for demo
        demo_candidates[candidate_id] = {
            "name": candidate_name,
            "filename": file.filename,
            "document_type": document_type,
            "upload_time": result.get("timestamp", "unknown"),
            "chunks_created": result["total_chunks_created"],
            "processing_time": result["total_processing_time_seconds"]
        }
        
        logger.info("Resume upload completed", candidate_id=candidate_id, chunks=result["total_chunks_created"])
        
        return {
            "message": "Resume processed successfully! 🎉",
            "candidate_id": candidate_id,
            "candidate_name": candidate_name,
            "filename": file.filename,
            "processing_results": {
                "documents_processed": result["documents_processed"],
                "total_chunks_created": result["total_chunks_created"],
                "processing_time_seconds": result["total_processing_time_seconds"]
            },
            "next_steps": [
                f"Use candidate_id '{candidate_id}' to score against a job",
                "Try the /score-candidate endpoint",
                "Generate a personalized email with /generate-email"
            ]
        }
        
    except Exception as e:
        logger.error("Resume upload failed", filename=file.filename, error=str(e))
        raise HTTPException(status_code=500, detail=f"Resume processing failed: {str(e)}")


@app.post("/score-candidate")
async def score_candidate(request: ScoringRequest):
    """
    Score a candidate against a job description using RAG.
    
    The system will:
    1. Generate embeddings for the job description
    2. Search for relevant candidate evidence in vector database
    3. Use GPT-4 to analyze fit and generate structured scoring
    4. Return detailed results with evidence and explanations
    """
    try:
        logger.info("Candidate scoring started", candidate_id=request.candidate_id)
        
        rag_engine = get_rag_engine()
        
        result = await rag_engine.score_candidate(
            candidate_id=request.candidate_id,
            job_description=request.job_description,
            ideal_candidate_profile=request.ideal_candidate_profile
        )
        
        scoring_data = result["scoring_data"]
        
        logger.info(
            "Candidate scoring completed",
            candidate_id=request.candidate_id,
            score=scoring_data.get("overall_score", 0)
        )
        
        return {
            "message": "Candidate scored successfully! 📊",
            "candidate_id": request.candidate_id,
            "scoring_results": {
                "overall_score": scoring_data.get("overall_score", 0),
                "overall_percentage": scoring_data.get("overall_percentage", 0),
                "fit_level": scoring_data.get("fit_level", "unknown"),
                "notable_strengths": scoring_data.get("notable_strengths", []),
                "potential_gaps": scoring_data.get("potential_gaps", []),
                "reasoning_summary": scoring_data.get("reasoning_summary", "")
            },
            "evidence": {
                "evidence_chunks_analyzed": len(result["evidence_used"]),
                "total_evidence_available": result["total_evidence_chunks"],
                "sample_evidence": result["evidence_used"][:3] if result["evidence_used"] else []
            },
            "processing_metadata": result["processing_metadata"]
        }
        
    except Exception as e:
        logger.error("Candidate scoring failed", candidate_id=request.candidate_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Candidate scoring failed: {str(e)}")


@app.post("/generate-email")
async def generate_email(request: EmailRequest):
    """
    Generate a personalized outreach email using RAG.
    
    The system will:
    1. Retrieve relevant candidate evidence for personalization
    2. Use GPT-4 to generate professional, personalized email content
    3. Include specific evidence and achievements
    4. Maintain the requested tone and style
    """
    try:
        logger.info("Email generation started", candidate_id=request.candidate_id, company=request.company_name)
        
        rag_engine = get_rag_engine()
        
        result = await rag_engine.generate_outreach_email(
            candidate_id=request.candidate_id,
            company_name=request.company_name,
            role_title=request.role_title,
            recruiter_name=request.recruiter_name,
            tone=request.tone
        )
        
        email_content = result["email_content"]
        
        logger.info("Email generation completed", candidate_id=request.candidate_id)
        
        return {
            "message": "Personalized email generated successfully! 📧",
            "candidate_id": request.candidate_id,
            "email_content": {
                "subject_line": email_content.get("subject_line", ""),
                "email_body": email_content.get("email_body", ""),
                "call_to_action": email_content.get("call_to_action", ""),
                "personalization_points": email_content.get("personalization_points", [])
            },
            "personalization_details": {
                "evidence_chunks_used": result["personalization_metadata"]["evidence_chunks_used"],
                "personalization_score": result["personalization_metadata"]["personalization_score"],
                "tone_analysis": email_content.get("tone_analysis", {}),
                "sample_evidence": result["evidence_used"][:2] if result["evidence_used"] else []
            },
            "generation_metadata": {
                "generation_time_seconds": result["personalization_metadata"]["generation_time_seconds"],
                "model_used": result["personalization_metadata"]["model_used"]
            }
        }
        
    except Exception as e:
        logger.error("Email generation failed", candidate_id=request.candidate_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Email generation failed: {str(e)}")


@app.get("/demo-candidates")
async def list_demo_candidates():
    """List all candidates uploaded in this demo session."""
    return {
        "message": "Demo candidates list",
        "total_candidates": len(demo_candidates),
        "candidates": demo_candidates
    }


@app.get("/search-evidence/{candidate_id}")
async def search_candidate_evidence(candidate_id: str, query: str):
    """Search for specific evidence in a candidate's documents."""
    try:
        rag_engine = get_rag_engine()
        
        evidence = await rag_engine.retrieve_candidate_evidence(
            query_text=query,
            candidate_id=candidate_id,
            top_k=5
        )
        
        return {
            "message": "Evidence search completed",
            "candidate_id": candidate_id,
            "query": query,
            "results_count": len(evidence),
            "evidence": evidence
        }
        
    except Exception as e:
        logger.error("Evidence search failed", candidate_id=candidate_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Evidence search failed: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    
    # Check if OpenAI API key is set
    try:
        settings = get_settings()
        if not settings.llm.openai_api_key or settings.llm.openai_api_key == "your-openai-api-key-here":
            print("⚠️  WARNING: OpenAI API key not set!")
            print("Please set OPENAI_API_KEY in your .env file")
            print("The demo will still start but LLM features will fail")
    except Exception as e:
        print(f"⚠️  Configuration issue: {e}")
    
    print("🚀 Starting RAG Demo Server...")
    print("📖 Documentation: http://localhost:8000/docs")
    print("🏠 Homepage: http://localhost:8000/")
    
    uvicorn.run(
        "working_demo:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
