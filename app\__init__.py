"""
Recruitment RAG System

A sophisticated Retrieval-Augmented Generation system for intelligent 
candidate scoring and personalized outreach email generation.
"""

__version__ = "1.0.0"
__author__ = "Elite Software Architect"
__description__ = "RAG-powered recruitment intelligence platform"

from app.core.config import get_settings

# Export main components for easy importing
from app.api import create_app

__all__ = [
    "create_app",
    "get_settings",
    "__version__",
    "__author__",
    "__description__"
]
