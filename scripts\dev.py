#!/usr/bin/env python3
"""
Development server runner with hot reload and debugging.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add app directory to path
sys.path.append(str(Path(__file__).parent.parent))

def run_server(host="127.0.0.1", port=8000, reload=True, workers=1):
    """Run the FastAPI server with uvicorn."""
    
    cmd = [
        "uvicorn",
        "app.main:app",
        f"--host={host}",
        f"--port={port}",
    ]
    
    if reload:
        cmd.append("--reload")
        cmd.extend(["--reload-dir", "app"])
    else:
        cmd.extend(["--workers", str(workers)])
    
    print(f"🚀 Starting RAG System server on http://{host}:{port}")
    print(f"📖 API Documentation: http://{host}:{port}/api/v1/docs")
    print(f"🔍 Health Check: http://{host}:{port}/health")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n✅ Server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        sys.exit(1)


def run_tests(coverage=True, verbose=False, markers=None):
    """Run the test suite."""
    
    cmd = ["pytest"]
    
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])
    
    if verbose:
        cmd.append("-v")
    
    if markers:
        cmd.extend(["-m", markers])
    
    print("🧪 Running tests...")
    
    try:
        result = subprocess.run(cmd, check=False)
        if result.returncode == 0:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed")
            sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted")


def format_code():
    """Format code with black and isort."""
    
    print("🎨 Formatting code...")
    
    # Run black
    subprocess.run(["black", "app", "tests", "scripts"], check=False)
    
    # Run isort
    subprocess.run(["isort", "app", "tests", "scripts"], check=False)
    
    print("✅ Code formatting completed")


def lint_code():
    """Lint code with flake8 and mypy."""
    
    print("🔍 Linting code...")
    
    # Run flake8
    print("Running flake8...")
    flake8_result = subprocess.run(["flake8", "app"], check=False)
    
    # Run mypy
    print("Running mypy...")
    mypy_result = subprocess.run(["mypy", "app"], check=False)
    
    if flake8_result.returncode == 0 and mypy_result.returncode == 0:
        print("✅ Code linting passed")
    else:
        print("❌ Code linting failed")
        sys.exit(1)


def setup_dev_environment():
    """Setup development environment."""
    
    print("🔧 Setting up development environment...")
    
    # Install pre-commit hooks
    subprocess.run(["pre-commit", "install"], check=False)
    
    # Create .env file if it doesn't exist
    env_file = Path(".env")
    if not env_file.exists():
        env_example = Path(".env.example")
        if env_example.exists():
            env_file.write_text(env_example.read_text())
            print("📝 Created .env file from .env.example")
            print("⚠️  Please update .env with your actual API keys")
        else:
            print("❌ .env.example not found")
    
    print("✅ Development environment setup completed")


def clean_project():
    """Clean project artifacts."""
    
    print("🧹 Cleaning project...")
    
    # Remove Python cache
    subprocess.run(["find", ".", "-type", "d", "-name", "__pycache__", "-exec", "rm", "-rf", "{}", "+"], check=False)
    subprocess.run(["find", ".", "-name", "*.pyc", "-delete"], check=False)
    
    # Remove test artifacts
    subprocess.run(["rm", "-rf", "htmlcov", ".coverage", ".pytest_cache"], check=False)
    
    # Remove build artifacts
    subprocess.run(["rm", "-rf", "build", "dist", "*.egg-info"], check=False)
    
    print("✅ Project cleaned")


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(description="RAG System Development Runner")
    parser.add_argument("command", choices=[
        "serve", "test", "format", "lint", "setup", "clean"
    ], help="Command to execute")
    
    # Server options
    parser.add_argument("--host", default="127.0.0.1", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    parser.add_argument("--workers", type=int, default=1, help="Number of workers")
    
    # Test options
    parser.add_argument("--no-coverage", action="store_true", help="Disable coverage")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose test output")
    parser.add_argument("-m", "--markers", help="Test markers to run")
    
    args = parser.parse_args()
    
    if args.command == "serve":
        run_server(
            host=args.host,
            port=args.port,
            reload=not args.no_reload,
            workers=args.workers
        )
    elif args.command == "test":
        run_tests(
            coverage=not args.no_coverage,
            verbose=args.verbose,
            markers=args.markers
        )
    elif args.command == "format":
        format_code()
    elif args.command == "lint":
        lint_code()
    elif args.command == "setup":
        setup_dev_environment()
    elif args.command == "clean":
        clean_project()


if __name__ == "__main__":
    main()
