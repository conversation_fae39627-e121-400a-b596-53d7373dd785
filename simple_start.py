#!/usr/bin/env python3
"""
Super simple setup - just installs minimal dependencies and runs the server.
"""

import subprocess
import sys
import os

def run_command(cmd):
    """Run a command and return success status."""
    try:
        subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e.stderr}")
        return False

def main():
    print("🚀 Quick RAG System Setup (Minimal Dependencies)")
    print("")
    
    # Install minimal requirements
    print("📦 Installing minimal dependencies...")
    if run_command("pip install fastapi uvicorn pydantic pydantic-settings python-dotenv"):
        print("✅ Dependencies installed!")
    else:
        print("❌ Failed to install dependencies")
        print("💡 Try: pip install fastapi uvicorn pydantic pydantic-settings python-dotenv")
        return False
    
    # Create .env if needed
    if not os.path.exists(".env"):
        print("📝 Creating .env file...")
        with open(".env", "w") as f:
            f.write("""# RAG System Environment Configuration
DEBUG=true
ENVIRONMENT=development
APP_NAME=Recruitment RAG System
APP_VERSION=1.0.0
API_PREFIX=/api/v1

# Database (using SQLite for simplicity)
DATABASE_URL=sqlite:///./rag_dev.db

# Security
SECRET_KEY=dev-secret-key-not-for-production

# File Storage
UPLOAD_DIR=./data/uploads
CHROMA_PERSIST_DIRECTORY=./data/chroma

# OpenAI (optional for API documentation viewing)
OPENAI_API_KEY=your-openai-api-key-here

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=console
""")
        print("✅ Created .env file")
    
    # Create data directories
    os.makedirs("data/uploads", exist_ok=True)
    os.makedirs("data/chroma", exist_ok=True)
    
    print("")
    print("🎉 Setup complete! Starting server...")
    print("")
    print("📍 Access Points:")
    print("🌐 API Documentation: http://localhost:8000/api/v1/docs")
    print("🔍 Health Check: http://localhost:8000/health") 
    print("📊 Alternative Docs: http://localhost:8000/api/v1/redoc")
    print("")
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Start the server
    try:
        import uvicorn
        from app.main import app
        
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n✅ Server stopped. Thanks for using RAG System!")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        print("💡 Try running: python run_local.py")

if __name__ == "__main__":
    main()
