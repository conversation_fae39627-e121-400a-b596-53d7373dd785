"""
LLM Service for handling language model interactions.
Provides abstraction layer for OpenAI, LM Studio, Anthropic, and local models.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Union
import openai
from openai import AsyncOpenAI
import httpx
import structlog
from abc import ABC, abstractmethod

from app.core.config import get_settings
from app.core.exceptions import LLMError, ConfigurationError
from app.core.logging import get_rag_logger

logger = get_rag_logger(__name__)


class BaseLLMProvider(ABC):
    """
    Abstract base class for LLM providers.
    """
    
    @abstractmethod
    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text using the LLM."""
        pass
    
    @abstractmethod
    async def generate_embeddings(
        self,
        texts: List[str],
        **kwargs
    ) -> List[List[float]]:
        """Generate embeddings for texts."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the LLM service is healthy."""
        pass


class OpenAIProvider(BaseLLMProvider):
    """
    OpenAI provider for GPT models and embeddings.
    """
    
    def __init__(self, api_key: str, model: str, embedding_model: str):
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = model
        self.embedding_model = embedding_model
        self.api_key = api_key
    
    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using OpenAI GPT models.
        
        Args:
            prompt: User prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            system_prompt: Optional system prompt
            **kwargs: Additional parameters
            
        Returns:
            dict: Generation result with metadata
        """
        start_time = time.time()
        
        try:
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            messages.append({"role": "user", "content": prompt})
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            
            generation_time = time.time() - start_time
            
            result = {
                "text": response.choices[0].message.content,
                "model": self.model,
                "usage": {
                    "input_tokens": response.usage.prompt_tokens,
                    "output_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "generation_time_seconds": generation_time,
                "finish_reason": response.choices[0].finish_reason
            }
            
            logger.log_llm_call(
                prompt_type="text_generation",
                input_tokens=response.usage.prompt_tokens,
                output_tokens=response.usage.completion_tokens,
                model=self.model,
                response_time=generation_time
            )
            
            return result
            
        except Exception as e:
            generation_time = time.time() - start_time
            error_msg = f"OpenAI generation failed: {str(e)}"
            
            logger.log_llm_call(
                prompt_type="text_generation",
                input_tokens=0,
                output_tokens=0,
                model=self.model,
                response_time=generation_time,
                error=error_msg
            )
            
            raise LLMError(error_msg, model=self.model)
    
    async def generate_embeddings(
        self,
        texts: List[str],
        **kwargs
    ) -> List[List[float]]:
        """
        Generate embeddings using OpenAI embedding models.
        
        Args:
            texts: List of texts to embed
            **kwargs: Additional parameters
            
        Returns:
            List[List[float]]: List of embedding vectors
        """
        start_time = time.time()
        
        try:
            # OpenAI has a limit on batch size, so chunk if necessary
            all_embeddings = []
            batch_size = 100  # Conservative batch size
            
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                
                response = await self.client.embeddings.create(
                    model=self.embedding_model,
                    input=batch,
                    **kwargs
                )
                
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
            
            generation_time = time.time() - start_time
            
            logger.log_embedding_operation(
                operation="generate_embeddings",
                text_length=sum(len(text) for text in texts),
                embedding_dimensions=len(all_embeddings[0]) if all_embeddings else 0,
                processing_time=generation_time,
                provider="openai"
            )
            
            return all_embeddings
            
        except Exception as e:
            generation_time = time.time() - start_time
            error_msg = f"OpenAI embedding generation failed: {str(e)}"
            
            logger.log_embedding_operation(
                operation="generate_embeddings",
                text_length=sum(len(text) for text in texts),
                processing_time=generation_time,
                provider="openai",
                error=error_msg
            )
            
            raise LLMError(error_msg, model=self.embedding_model)
    
    async def health_check(self) -> bool:
        """
        Check OpenAI service health.
        
        Returns:
            bool: True if service is healthy
        """
        try:
            # Simple test call
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Test"}],
                max_tokens=1
            )
            return True
        except Exception as e:
            logger.error(f"OpenAI health check failed: {str(e)}")
            return False


class LMStudioProvider(BaseLLMProvider):
    """
    LM Studio provider for local models with OpenAI-compatible API.
    """
    
    def __init__(self, host: str, api_key: str, model: str, embedding_model: Optional[str] = None):
        self.host = host.rstrip('/')
        self.api_key = api_key
        self.model = model
        self.embedding_model = embedding_model
        
        # Create OpenAI client pointing to LM Studio
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=f"{self.host}/v1"
        )
    
    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using LM Studio local models.
        
        Args:
            prompt: User prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            system_prompt: Optional system prompt
            **kwargs: Additional parameters
            
        Returns:
            dict: Generation result with metadata
        """
        start_time = time.time()
        
        try:
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            messages.append({"role": "user", "content": prompt})
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            
            generation_time = time.time() - start_time
            
            # LM Studio may not always provide usage stats
            usage = getattr(response, 'usage', None)
            if usage:
                input_tokens = usage.prompt_tokens
                output_tokens = usage.completion_tokens
                total_tokens = usage.total_tokens
            else:
                # Estimate tokens if not provided
                input_tokens = len(prompt.split()) * 1.3  # rough estimate
                output_tokens = len(response.choices[0].message.content.split()) * 1.3
                total_tokens = input_tokens + output_tokens
            
            result = {
                "text": response.choices[0].message.content,
                "model": f"lmstudio:{self.model}",
                "usage": {
                    "input_tokens": int(input_tokens),
                    "output_tokens": int(output_tokens),
                    "total_tokens": int(total_tokens)
                },
                "generation_time_seconds": generation_time,
                "finish_reason": response.choices[0].finish_reason
            }
            
            logger.log_llm_call(
                prompt_type="text_generation",
                input_tokens=int(input_tokens),
                output_tokens=int(output_tokens),
                model=f"lmstudio:{self.model}",
                response_time=generation_time
            )
            
            return result
            
        except Exception as e:
            generation_time = time.time() - start_time
            error_msg = f"LM Studio generation failed: {str(e)}"
            
            logger.log_llm_call(
                prompt_type="text_generation",
                input_tokens=0,
                output_tokens=0,
                model=f"lmstudio:{self.model}",
                response_time=generation_time,
                error=error_msg
            )
            
            raise LLMError(error_msg, model=f"lmstudio:{self.model}")
    
    async def generate_embeddings(
        self,
        texts: List[str],
        **kwargs
    ) -> List[List[float]]:
        """
        Generate embeddings using LM Studio (if supported by the model).
        
        Args:
            texts: List of texts to embed
            **kwargs: Additional parameters
            
        Returns:
            List[List[float]]: List of embedding vectors
        """
        start_time = time.time()
        
        if not self.embedding_model:
            # Fallback: use a simple text-based embedding (TF-IDF like)
            logger.warning("LM Studio embedding model not configured, using fallback embeddings")
            
            # Simple fallback embedding (for development/testing)
            import hashlib
            import numpy as np
            
            embeddings = []
            for text in texts:
                # Create a simple hash-based embedding (not ideal but functional)
                hash_bytes = hashlib.md5(text.encode()).digest()
                embedding = [float(b) / 255.0 for b in hash_bytes[:16]]  # 16-dim embedding
                # Pad to standard embedding size (384 for smaller models)
                embedding.extend([0.0] * (384 - len(embedding)))
                embeddings.append(embedding[:384])
            
            generation_time = time.time() - start_time
            
            logger.log_embedding_operation(
                operation="generate_embeddings_fallback",
                text_length=sum(len(text) for text in texts),
                embedding_dimensions=384,
                processing_time=generation_time,
                provider="lmstudio_fallback"
            )
            
            return embeddings
        
        try:
            # Try using LM Studio embeddings endpoint if available
            all_embeddings = []
            batch_size = 50  # Conservative batch size for local models
            
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                
                response = await self.client.embeddings.create(
                    model=self.embedding_model,
                    input=batch,
                    **kwargs
                )
                
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
            
            generation_time = time.time() - start_time
            
            logger.log_embedding_operation(
                operation="generate_embeddings",
                text_length=sum(len(text) for text in texts),
                embedding_dimensions=len(all_embeddings[0]) if all_embeddings else 0,
                processing_time=generation_time,
                provider="lmstudio"
            )
            
            return all_embeddings
            
        except Exception as e:
            generation_time = time.time() - start_time
            error_msg = f"LM Studio embedding generation failed: {str(e)}"
            
            logger.log_embedding_operation(
                operation="generate_embeddings",
                text_length=sum(len(text) for text in texts),
                processing_time=generation_time,
                provider="lmstudio",
                error=error_msg
            )
            
            raise LLMError(error_msg, model=f"lmstudio:{self.embedding_model}")
    
    async def health_check(self) -> bool:
        """
        Check LM Studio service health.
        
        Returns:
            bool: True if service is healthy
        """
        try:
            # Test connection to LM Studio
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.host}/v1/models", timeout=5.0)
                if response.status_code == 200:
                    models = response.json()
                    # Check if our model is available
                    available_models = [model.get("id", "") for model in models.get("data", [])]
                    if self.model in available_models or len(available_models) > 0:
                        return True
                return False
        except Exception as e:
            logger.error(f"LM Studio health check failed: {str(e)}")
            return False


class LLMService:
    """
    Main LLM service that manages different providers.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.provider = self._initialize_provider()
    
    def _initialize_provider(self) -> BaseLLMProvider:
        """Initialize the configured LLM provider."""
        
        provider = self.settings.llm.llm_provider.lower()
        
        if provider == "openai":
            if not self.settings.llm.openai_api_key:
                raise ConfigurationError(
                    "OpenAI API key required for OpenAI provider",
                    config_key="openai_api_key"
                )
            return OpenAIProvider(
                api_key=self.settings.llm.openai_api_key,
                model=self.settings.llm.openai_model,
                embedding_model=self.settings.llm.openai_embedding_model
            )
        
        elif provider == "lmstudio":
            return LMStudioProvider(
                host=self.settings.llm.lmstudio_host,
                api_key=self.settings.llm.lmstudio_api_key,
                model=self.settings.llm.lmstudio_model,
                embedding_model=self.settings.llm.lmstudio_embedding_model
            )
        
        elif provider == "ollama":
            # TODO: Implement Ollama provider
            raise ConfigurationError(
                "Ollama provider not yet implemented",
                config_key="llm_provider"
            )
        
        elif provider == "anthropic":
            # TODO: Implement Anthropic provider
            raise ConfigurationError(
                "Anthropic provider not yet implemented",
                config_key="llm_provider"
            )
        
        else:
            # Fallback logic for backward compatibility
            if self.settings.llm.openai_api_key:
                logger.info("No provider specified, defaulting to OpenAI")
                return OpenAIProvider(
                    api_key=self.settings.llm.openai_api_key,
                    model=self.settings.llm.openai_model,
                    embedding_model=self.settings.llm.openai_embedding_model
                )
            else:
                raise ConfigurationError(
                    f"Unsupported LLM provider: {provider}. Supported: openai, lmstudio",
                    config_key="llm_provider"
                )
    
    async def generate_candidate_score(
        self,
        job_description: str,
        candidate_data: str,
        evidence_snippets: List[str],
        ideal_profile: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate candidate scoring using RAG prompts.
        
        Args:
            job_description: Job description text
            candidate_data: Candidate information
            evidence_snippets: Retrieved evidence from vector search
            ideal_profile: Optional ideal candidate profile
            
        Returns:
            dict: Structured scoring result
        """
        system_prompt = """You are an AI recruiter assistant specializing in candidate evaluation.
You analyze candidates against job requirements and provide structured, evidence-based scoring.

Your responses must be in valid JSON format with this exact structure:
{
  "candidate_name": "...",
  "overall_score": 0.85,
  "overall_percentage": 85,
  "fit_level": "excellent",
  "requirements": [
    {
      "requirement": "...",
      "evidence": ["snippet1", "snippet2"],
      "score": 0.9,
      "explanation": "..."
    }
  ],
  "notable_strengths": ["...", "..."],
  "potential_gaps": ["...", "..."],
  "reasoning_summary": "..."
}

Score scale: 0.0-1.0 (convert to 0-100% for percentage)
Fit levels: "excellent" (85-100%), "good" (70-84%), "fair" (55-69%), "poor" (<55%)"""
        
        user_prompt = f"""
CANDIDATE EVALUATION REQUEST:

JOB DESCRIPTION:
{job_description}

{"IDEAL CANDIDATE PROFILE:" if ideal_profile else ""}
{ideal_profile or ""}

CANDIDATE INFORMATION:
{candidate_data}

EVIDENCE SNIPPETS FROM CANDIDATE DOCUMENTS:
{chr(10).join(f"- {snippet}" for snippet in evidence_snippets[:10])}

Please analyze this candidate against the job requirements and provide a structured evaluation.
Focus on specific evidence and provide clear reasoning for scores.
"""
        
        try:
            result = await self.provider.generate_text(
                prompt=user_prompt,
                system_prompt=system_prompt,
                max_tokens=2000,
                temperature=0.1
            )
            
            return result
            
        except Exception as e:
            raise LLMError(f"Candidate scoring failed: {str(e)}")
    
    async def generate_outreach_email(
        self,
        candidate_name: str,
        candidate_evidence: List[str],
        company_name: str,
        role_title: str,
        recruiter_name: Optional[str] = None,
        tone: str = "professional"
    ) -> Dict[str, Any]:
        """
        Generate personalized outreach email.
        
        Args:
            candidate_name: Candidate's name
            candidate_evidence: Evidence snippets for personalization
            company_name: Company name
            role_title: Role title
            recruiter_name: Recruiter's name
            tone: Email tone
            
        Returns:
            dict: Generated email content
        """
        system_prompt = f"""You are a professional recruiter writing personalized outreach emails.

Tone: {tone}
Guidelines:
- 150-200 words maximum
- Professional and respectful
- Personalized with specific evidence
- Clear call to action
- Engaging subject line

Your response must be in valid JSON format:
{{
  "subject_line": "...",
  "email_body": "...",
  "call_to_action": "...",
  "personalization_points": ["point1", "point2"],
  "tone_analysis": {{"professional": 0.9, "friendly": 0.7}}
}}"""
        
        user_prompt = f"""
OUTREACH EMAIL REQUEST:

Candidate: {candidate_name}
Company: {company_name}
Role: {role_title}
Recruiter: {recruiter_name or "Hiring Team"}

CANDIDATE EVIDENCE FOR PERSONALIZATION:
{chr(10).join(f"- {evidence}" for evidence in candidate_evidence[:5])}

Write a compelling, personalized outreach email that highlights specific evidence from their background.
"""
        
        try:
            result = await self.provider.generate_text(
                prompt=user_prompt,
                system_prompt=system_prompt,
                max_tokens=1000,
                temperature=0.2
            )
            
            return result
            
        except Exception as e:
            raise LLMError(f"Email generation failed: {str(e)}")
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for texts."""
        return await self.provider.generate_embeddings(texts)
    
    async def health_check(self) -> bool:
        """Check LLM service health."""
        return await self.provider.health_check()


# Global service instance
_llm_service: Optional[LLMService] = None


def get_llm_service() -> LLMService:
    """
    Get or create LLM service instance.
    
    Returns:
        LLMService: Service instance
    """
    global _llm_service
    
    if _llm_service is None:
        _llm_service = LLMService()
    
    return _llm_service
