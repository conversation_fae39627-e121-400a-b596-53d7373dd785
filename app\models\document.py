"""
Document model for storing file information and processing status.
"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, ForeignKey, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class Document(Base):
    """
    Document model for storing uploaded files and their processing status.
    """
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # File information
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100))
    file_hash = Column(String(64))  # SHA-256 hash for deduplication
    
    # Document classification
    document_type = Column(String(50), nullable=False)  # resume, cover_letter, portfolio, etc.
    source = Column(String(100))  # upload, linkedin, github, etc.
    
    # Processing status
    processing_status = Column(String(50), default="pending")  # pending, processing, completed, failed
    processing_error = Column(Text)
    processing_started_at = Column(DateTime(timezone=True))
    processing_completed_at = Column(DateTime(timezone=True))
    
    # Content extraction
    extracted_text = Column(Text)
    text_length = Column(Integer)
    language_detected = Column(String(10))
    
    # Vector processing
    chunks_created = Column(Integer, default=0)
    embedding_status = Column(String(50), default="pending")  # pending, processing, completed, failed
    embedding_model = Column(String(100))
    embedding_dimensions = Column(Integer)
    
    # Metadata and analysis
    metadata = Column(Text)  # JSON string for flexible metadata
    content_summary = Column(Text)  # AI-generated summary
    key_skills_extracted = Column(Text)  # JSON array of extracted skills
    
    # Associations
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("candidates.id"))
    uploaded_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    candidate = relationship("Candidate", back_populates="documents")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Document(id={self.id}, filename={self.filename}, type={self.document_type})>"
    
    @property
    def file_size_mb(self) -> float:
        """File size in megabytes."""
        return round(self.file_size / (1024 * 1024), 2)
    
    @property
    def is_processed(self) -> bool:
        """Check if document is fully processed."""
        return self.processing_status == "completed" and self.embedding_status == "completed"
    
    @property
    def processing_time_seconds(self) -> float:
        """Calculate processing time in seconds."""
        if self.processing_started_at and self.processing_completed_at:
            delta = self.processing_completed_at - self.processing_started_at
            return delta.total_seconds()
        return 0.0


class DocumentChunk(Base):
    """
    Document chunk model for storing text chunks and their embeddings.
    """
    __tablename__ = "document_chunks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Chunk information
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    content = Column(Text, nullable=False)
    content_length = Column(Integer, nullable=False)
    
    # Position in document
    start_char = Column(Integer)
    end_char = Column(Integer)
    page_number = Column(Integer)
    
    # Embedding information
    embedding_vector = Column(Text)  # JSON array of embedding values
    embedding_model = Column(String(100))
    embedding_dimensions = Column(Integer)
    
    # Metadata
    chunk_type = Column(String(50))  # paragraph, section, list_item, etc.
    importance_score = Column(Float)  # Calculated importance (0.0 to 1.0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    document = relationship("Document", back_populates="chunks")
    
    def __repr__(self):
        return f"<DocumentChunk(id={self.id}, doc_id={self.document_id}, index={self.chunk_index})>"
