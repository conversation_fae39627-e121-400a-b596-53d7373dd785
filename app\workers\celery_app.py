"""
Celery application configuration for background task processing.
Handles document processing, embedding generation, and other async operations.
"""

from celery import Celery
from celery.signals import worker_init, worker_process_init
import structlog

from app.core.config import get_settings

logger = structlog.get_logger(__name__)

# Get settings
settings = get_settings()

# Create Celery app
celery_app = Celery(
    "rag_workers",
    broker=settings.redis.celery_broker_url,
    backend=settings.redis.celery_result_backend,
    include=[
        "app.workers.document_tasks",
        "app.workers.embedding_tasks", 
        "app.workers.scoring_tasks",
        "app.workers.email_tasks"
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    
    # Task routing
    task_routes={
        "app.workers.document_tasks.*": {"queue": "documents"},
        "app.workers.embedding_tasks.*": {"queue": "embeddings"},
        "app.workers.scoring_tasks.*": {"queue": "scoring"},
        "app.workers.email_tasks.*": {"queue": "emails"},
    },
    
    # Task execution
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    task_reject_on_worker_lost=True,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Retry settings
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Beat schedule (for periodic tasks)
    beat_schedule={
        "cleanup-old-results": {
            "task": "app.workers.maintenance_tasks.cleanup_old_results",
            "schedule": 3600.0,  # Every hour
        },
        "update-vector-store-stats": {
            "task": "app.workers.maintenance_tasks.update_vector_store_stats", 
            "schedule": 300.0,  # Every 5 minutes
        },
    },
)


@worker_init.connect
def worker_init_handler(sender=None, conf=None, **kwargs):
    """Initialize worker with logging and configuration."""
    logger.info(
        "Celery worker initializing",
        worker_name=sender,
        queues=conf.get("task_routes", {}).keys() if conf else None
    )


@worker_process_init.connect
def worker_process_init_handler(sender=None, **kwargs):
    """Initialize worker process."""
    logger.info("Celery worker process started", pid=sender.pid)


# Export for use in tasks
__all__ = ["celery_app"]
