#!/usr/bin/env python3
"""
Final System Test for RAG Production System
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

def main():
    print("RAG Production System - Final Test")
    print("=" * 50)
    
    # Test basic imports
    print("1. Testing imports...")
    try:
        import fastapi
        import sqlalchemy  
        import openai
        print("   [OK] Core dependencies")
    except ImportError as e:
        print(f"   [ERROR] Missing: {e}")
        return False
    
    # Test configuration
    print("2. Testing configuration...")
    try:
        from app.core.config import get_settings
        settings = get_settings()
        print(f"   [OK] Config: {settings.app_name}")
        
        if hasattr(settings.llm, 'openai_api_key'):
            if settings.llm.openai_api_key != "your-openai-api-key-here":
                print("   [OK] OpenAI key configured")
            else:
                print("   [WARNING] Set OPENAI_API_KEY in .env")
        
    except Exception as e:
        print(f"   [ERROR] Config: {e}")
        return False
    
    # Test services
    print("3. Testing services...")
    try:
        from app.services.document_processor import get_document_processor
        processor = get_document_processor()
        chunks = processor.create_text_chunks("Test document text.")
        if chunks:
            print("   [OK] Document processing")
        
        from app.services.rag_engine import get_rag_engine  
        rag_engine = get_rag_engine()
        print("   [OK] RAG engine")
        
    except Exception as e:
        print(f"   [ERROR] Services: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("SUCCESS: Core system validated!")
    print("\nNext steps:")
    print("1. Set OPENAI_API_KEY in .env file")
    print("2. Run: python working_demo.py")
    print("3. Visit: http://localhost:8000")
    print("4. Test with: python production_demo.py")
    
    return True

if __name__ == "__main__":
    main()
