"""
Job posting model for storing job descriptions and requirements.
"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class JobPosting(Base):
    """
    Job posting model for storing job descriptions and requirements.
    """
    __tablename__ = "job_postings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Basic job information
    title = Column(String(255), nullable=False)
    company = Column(String(255), nullable=False)
    department = Column(String(255))
    location = Column(String(255), nullable=False)
    
    # Job details
    remote_allowed = Column(Boolean, default=False)
    job_type = Column(String(50), default="full-time")  # full-time, part-time, contract
    experience_level = Column(String(50), default="mid")  # entry, mid, senior, executive
    
    # Job content
    description = Column(Text, nullable=False)
    summary = Column(Text)  # AI-generated summary
    
    # Requirements and qualifications
    requirements = Column(ARRAY(String))  # Required skills/qualifications
    nice_to_have = Column(ARRAY(String))  # Preferred qualifications
    responsibilities = Column(ARRAY(String))  # Key responsibilities
    benefits = Column(ARRAY(String))  # Benefits offered
    
    # Compensation
    salary_range_min = Column(Integer)
    salary_range_max = Column(Integer)
    currency = Column(String(10), default="USD")
    
    # Cultural and fit information
    ideal_candidate_profile = Column(Text)
    company_culture = Column(Text)
    team_description = Column(Text)
    
    # Status and metadata
    is_active = Column(Boolean, default=True)
    is_urgent = Column(Boolean, default=False)
    positions_available = Column(Integer, default=1)
    
    # Posting information
    posted_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    external_posting_url = Column(String(500))
    application_deadline = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    scores = relationship("CandidateScore", back_populates="job", cascade="all, delete-orphan")
    outreach_emails = relationship("OutreachEmail", back_populates="job", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<JobPosting(id={self.id}, title={self.title}, company={self.company})>"
    
    @property
    def salary_range_display(self) -> str:
        """Display formatted salary range."""
        if self.salary_range_min and self.salary_range_max:
            return f"${self.salary_range_min:,} - ${self.salary_range_max:,} {self.currency}"
        elif self.salary_range_min:
            return f"${self.salary_range_min:,}+ {self.currency}"
        else:
            return "Salary not specified"
    
    @property
    def requirements_count(self) -> int:
        """Count of total requirements."""
        req_count = len(self.requirements) if self.requirements else 0
        nice_count = len(self.nice_to_have) if self.nice_to_have else 0
        return req_count + nice_count
