"""
Unit tests for the RAG Engine core functionality.
"""

import pytest
import asyncio
from unittest.mock import <PERSON><PERSON>, AsyncMock, patch
import json

from app.services.rag_engine import RAGEngine
from app.core.exceptions import RAGException


class TestRAGEngine:
    """Test cases for RAG Engine."""
    
    @pytest.fixture
    def rag_engine(self):
        """Create RAG engine instance for testing."""
        return RAGEngine()
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_retrieve_candidate_evidence_success(
        self, 
        rag_engine, 
        mock_embedding_response, 
        mock_vector_search_results
    ):
        """Test successful evidence retrieval."""
        
        # Mock LLM service
        with patch.object(rag_engine.llm_service, 'generate_embeddings') as mock_embeddings:
            mock_embeddings.return_value = mock_embedding_response
            
            # Mock vector store
            with patch.object(rag_engine.vector_store, 'search_candidate_evidence') as mock_search:
                mock_search.return_value = mock_vector_search_results
                
                # Test evidence retrieval
                results = await rag_engine.retrieve_candidate_evidence(
                    query_text="Python developer experience",
                    candidate_id="test-candidate-123",
                    top_k=5
                )
                
                # Assertions
                assert len(results) == 2
                assert results[0]["similarity_score"] == 0.92
                assert "Python" in results[0]["content"]
                
                # Verify method calls
                mock_embeddings.assert_called_once_with(["Python developer experience"])
                mock_search.assert_called_once()
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_score_candidate_success(
        self, 
        rag_engine, 
        mock_openai_response,
        mock_vector_search_results
    ):
        """Test successful candidate scoring."""
        
        # Mock evidence retrieval
        with patch.object(rag_engine, 'retrieve_candidate_evidence') as mock_retrieve:
            mock_retrieve.return_value = mock_vector_search_results
            
            # Mock LLM service
            with patch.object(rag_engine.llm_service, 'generate_candidate_score') as mock_score:
                mock_score.return_value = mock_openai_response
                
                # Test candidate scoring
                result = await rag_engine.score_candidate(
                    candidate_id="test-candidate-123",
                    job_description="Looking for a Senior Python Developer with 5+ years experience",
                    ideal_candidate_profile="Strong technical background in Python and web development"
                )
                
                # Assertions
                assert result["candidate_id"] == "test-candidate-123"
                assert "scoring_data" in result
                assert "evidence_used" in result
                assert result["total_evidence_chunks"] == 2
                
                # Verify scoring data
                scoring_data = result["scoring_data"]
                assert scoring_data["overall_score"] == 0.85
                assert scoring_data["fit_level"] == "excellent"
                
                # Verify method calls
                assert mock_retrieve.call_count >= 1
                mock_score.assert_called_once()
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_outreach_email_success(
        self, 
        rag_engine,
        mock_vector_search_results
    ):
        """Test successful outreach email generation."""
        
        mock_email_response = {
            "text": '{"subject_line": "Exciting Python Developer role at TechCorp", "email_body": "Hi John, I came across your profile...", "call_to_action": "Would you be interested in a brief call?", "personalization_points": ["Python experience", "FastAPI projects"], "tone_analysis": {"professional": 0.9}}',
            "model": "gpt-4-1106-preview",
            "usage": {"total_tokens": 300}
        }
        
        # Mock evidence retrieval
        with patch.object(rag_engine, 'retrieve_candidate_evidence') as mock_retrieve:
            mock_retrieve.return_value = mock_vector_search_results
            
            # Mock LLM service
            with patch.object(rag_engine.llm_service, 'generate_outreach_email') as mock_email:
                mock_email.return_value = mock_email_response
                
                # Test email generation
                result = await rag_engine.generate_outreach_email(
                    candidate_id="test-candidate-123",
                    company_name="TechCorp",
                    role_title="Senior Python Developer",
                    recruiter_name="Jane Smith"
                )
                
                # Assertions
                assert result["candidate_id"] == "test-candidate-123"
                assert "email_content" in result
                assert "evidence_used" in result
                
                # Verify email content
                email_content = result["email_content"]
                assert "subject_line" in email_content
                assert "email_body" in email_content
                assert "TechCorp" in email_content["subject_line"]
                
                # Verify method calls
                assert mock_retrieve.call_count >= 1
                mock_email.assert_called_once()
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_process_and_store_candidate_documents_success(self, rag_engine):
        """Test successful document processing and storage."""
        
        mock_processing_result = {
            "file_path": "/app/data/uploads/test.pdf",
            "chunks": [
                {"content": "John Doe - Senior Python Developer", "chunk_index": 0},
                {"content": "5 years experience with Python and FastAPI", "chunk_index": 1}
            ],
            "chunks_created": 2,
            "text_length": 500,
            "processing_time_seconds": 2.0
        }
        
        mock_embeddings = [[0.1] * 1536, [0.2] * 1536]
        
        # Mock document processor
        with patch.object(rag_engine.document_processor, 'process_document') as mock_process:
            mock_process.return_value = mock_processing_result
            
            # Mock LLM service
            with patch.object(rag_engine.llm_service, 'generate_embeddings') as mock_embed:
                mock_embed.return_value = mock_embeddings
                
                # Mock vector store
                with patch.object(rag_engine.vector_store, 'add_candidate_documents') as mock_store:
                    mock_store.return_value = ["doc1", "doc2"]
                    
                    # Test document processing
                    documents = [
                        {
                            "file_content": b"Sample PDF content",
                            "filename": "resume.pdf",
                            "document_type": "resume"
                        }
                    ]
                    
                    result = await rag_engine.process_and_store_candidate_documents(
                        candidate_id="test-candidate-123",
                        documents=documents
                    )
                    
                    # Assertions
                    assert result["candidate_id"] == "test-candidate-123"
                    assert result["documents_processed"] == 1
                    assert result["total_chunks_created"] == 2
                    assert result["status"] == "completed"
                    
                    # Verify method calls
                    mock_process.assert_called_once()
                    mock_embed.assert_called_once()
                    mock_store.assert_called_once()
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_score_candidate_with_llm_error(self, rag_engine):
        """Test candidate scoring with LLM error."""
        
        # Mock evidence retrieval to succeed
        with patch.object(rag_engine, 'retrieve_candidate_evidence') as mock_retrieve:
            mock_retrieve.return_value = []
            
            # Mock LLM service to fail
            with patch.object(rag_engine.llm_service, 'generate_candidate_score') as mock_score:
                mock_score.side_effect = Exception("LLM API error")
                
                # Test that RAGException is raised
                with pytest.raises(RAGException) as exc_info:
                    await rag_engine.score_candidate(
                        candidate_id="test-candidate-123",
                        job_description="Test job description"
                    )
                
                assert "LLM API error" in str(exc_info.value)
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_health_check_all_healthy(self, rag_engine):
        """Test health check when all components are healthy."""
        
        # Mock all services as healthy
        with patch.object(rag_engine.llm_service, 'health_check') as mock_llm:
            mock_llm.return_value = True
            
            with patch.object(rag_engine.vector_store, 'health_check') as mock_vector:
                mock_vector.return_value = True
                
                # Test health check
                result = await rag_engine.health_check()
                
                # Assertions
                assert result["rag_engine"] == "healthy"
                assert result["components"]["llm_service"]["status"] == "healthy"
                assert result["components"]["vector_store"]["status"] == "healthy"
                assert result["components"]["document_processor"]["status"] == "healthy"
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_health_check_degraded(self, rag_engine):
        """Test health check when some components are unhealthy."""
        
        # Mock LLM as unhealthy, vector store as healthy
        with patch.object(rag_engine.llm_service, 'health_check') as mock_llm:
            mock_llm.side_effect = Exception("LLM service error")
            
            with patch.object(rag_engine.vector_store, 'health_check') as mock_vector:
                mock_vector.return_value = True
                
                # Test health check
                result = await rag_engine.health_check()
                
                # Assertions
                assert result["rag_engine"] == "degraded"
                assert result["components"]["llm_service"]["status"] == "unhealthy"
                assert result["components"]["vector_store"]["status"] == "healthy"
