# =============================================================================
# RAG System Production Dependencies
# =============================================================================

# Core FastAPI Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Database & ORM
sqlalchemy==2.0.23
asyncpg==0.29.0  # Async PostgreSQL driver
psycopg2-binary==2.9.9  # Backup sync driver
alembic==1.13.1

# Vector Database & AI
chromadb==0.4.18
openai==1.3.7

# Document Processing
PyMuPDF==1.23.8
pdfplumber==0.10.3
python-docx==1.0.1
python-multipart==0.0.6

# Background Task Processing
celery==5.3.4
redis==5.0.1
flower==2.0.1  # Celery monitoring

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==41.0.8

# HTTP Client & Web
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Data Processing & Analysis
pandas==2.1.4
numpy==1.24.4
nltk==3.8.1

# Logging & Monitoring
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0  # Error tracking

# Configuration & Environment
python-dotenv==1.0.0
click==8.1.7  # CLI tools

# Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
factory-boy==3.3.0  # Test data factories

# Development & Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Optional: Advanced NLP (uncomment if needed)
# spacy==3.7.2
# transformers==4.35.2
