#!/usr/bin/env python3
"""
Quick System Validation for RAG Production System
Tests core functionality without database dependencies.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def test_core_system():
    """Test core system functionality."""
    print("🧪 RAG Production System - Quick Validation")
    print("=" * 50)
    
    # Test 1: Basic imports
    print("1. Testing core imports...")
    try:
        # Test basic Python requirements
        import fastapi
        import sqlalchemy
        import openai
        import structlog
        print("   ✅ Core dependencies available")
    except ImportError as e:
        print(f"   ❌ Missing dependency: {e}")
        return False
    
    # Test 2: Configuration
    print("\n2. Testing configuration...")
    try:
        from app.core.config import get_settings
        settings = get_settings()
        print(f"   ✅ Configuration loaded: {settings.app_name}")
        
        # Check OpenAI key
        if hasattr(settings.llm, 'openai_api_key'):
            if settings.llm.openai_api_key and settings.llm.openai_api_key != "your-openai-api-key-here":
                print("   ✅ OpenAI API key configured")
            else:
                print("   ⚠️  OpenAI API key not configured (set OPENAI_API_KEY in .env)")
        
    except Exception as e:
        print(f"   ❌ Configuration error: {e}")
        return False
    
    # Test 3: Core services
    print("\n3. Testing core services...")
    try:
        from app.services.document_processor import get_document_processor
        processor = get_document_processor()
        
        # Test document processing
        test_text = "This is a test document for RAG system validation."
        chunks = processor.create_text_chunks(test_text)
        
        if chunks and len(chunks) > 0:
            print("   ✅ Document processing works")
        else:
            print("   ❌ Document processing failed")
            
    except Exception as e:
        print(f"   ❌ Service test error: {e}")
        return False
    
    # Test 4: API structure
    print("\n4. Testing API structure...")
    try:
        from app.api.v1 import api_router
        from app.main import create_app
        
        # Test API creation
        app = create_app()
        print("   ✅ API application created successfully")
        
    except Exception as e:
        print(f"   ❌ API structure error: {e}")
        return False
    
    return True


def main():
    """Main validation function."""
    success = test_core_system()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ CORE SYSTEM VALIDATION PASSED!")
        print("\n🚀 Your RAG system is ready to run!")
        print("\nNext steps:")
        print("   1. Set OPENAI_API_KEY in .env file (if not done)")
        print("   2. Start production server: python production_server.py")
        print("   3. Test with demo: python production_demo.py")
        print("   4. View API docs: http://localhost:8000/api/v1/docs")
        
    else:
        print("❌ VALIDATION FAILED")
        print("Please check the errors above and fix any issues")
        print("\n💡 Troubleshooting:")
        print("   - Install dependencies: pip install -r requirements.txt")
        print("   - Check Python version: Python 3.11+ recommended")
        print("   - Verify file structure and permissions")

if __name__ == "__main__":
    main()
