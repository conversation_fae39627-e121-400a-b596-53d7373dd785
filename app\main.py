"""
FastAPI application factory and main app configuration.
Handles app initialization, middleware setup, and route registration.
"""

from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog
import time
import uuid

from app.core.config import get_settings
from app.api.v1 import api_router
from app.core.exceptions import RAGException
from app.core.logging import setup_logging
from app.db.database import init_db, close_db


# Setup structured logging
logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    Handles startup and shutdown events.
    """
    settings = get_settings()
    
    # Startup
    logger.info("Starting RAG System", version=settings.app_version)
    
    # Initialize database
    try:
        await init_db()
        logger.info("Database initialized")
    except Exception as e:
        logger.warning("Database initialization failed", error=str(e))
    
    # Initialize vector database
    # This will be handled by the vector service
    logger.info("Vector database ready")
    
    yield
    
    # Shutdown
    logger.info("Shutting down RAG System")
    try:
        await close_db()
    except Exception as e:
        logger.warning("Database shutdown failed", error=str(e))


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured application instance
    """
    settings = get_settings()
    
    # Setup logging
    try:
        setup_logging(
            level=settings.monitoring.log_level,
            format_type=settings.monitoring.log_format
        )
    except Exception as e:
        print(f"Logging setup failed: {e}")
    
    # Create FastAPI instance
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="Recruitment RAG System for intelligent candidate analysis",
        debug=settings.debug,
        lifespan=lifespan,
        openapi_url=f"{settings.api_prefix}/openapi.json" if settings.debug else None,
        docs_url=f"{settings.api_prefix}/docs" if settings.debug else None,
        redoc_url=f"{settings.api_prefix}/redoc" if settings.debug else None,
    )
    
    # Add middleware
    setup_middleware(app, settings)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    # Include API routes
    app.include_router(
        api_router,
        prefix=settings.api_prefix
    )
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Simple health check endpoint."""
        return {
            "status": "healthy",
            "service": settings.app_name,
            "version": settings.app_version,
            "timestamp": time.time()
        }
    
    return app


def setup_middleware(app: FastAPI, settings) -> None:
    """
    Configure application middleware.
    
    Args:
        app: FastAPI application instance
        settings: Application settings
    """
    
    # Request ID middleware
    @app.middleware("http")
    async def add_request_id(request, call_next):
        """Add unique request ID to each request."""
        request_id = str(uuid.uuid4())
        
        # Add to request state
        request.state.request_id = request_id
        
        # Log request start
        try:
            logger.info(
                "Request started",
                request_id=request_id,
                method=request.method,
                url=str(request.url)
            )
        except:
            pass
        
        start_time = time.time()
        response = await call_next(request)
        duration = time.time() - start_time
        
        # Add to response headers
        response.headers["X-Request-ID"] = request_id
        
        # Log request completion
        try:
            logger.info(
                "Request completed",
                request_id=request_id,
                status_code=response.status_code,
                duration=duration
            )
        except:
            pass
        
        return response
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.debug else ["https://yourdomain.com"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Trusted host middleware (security)
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["yourdomain.com", "*.yourdomain.com"]
        )


def setup_exception_handlers(app: FastAPI) -> None:
    """
    Setup global exception handlers.
    
    Args:
        app: FastAPI application instance
    """
    
    @app.exception_handler(RAGException)
    async def rag_exception_handler(request, exc: RAGException):
        """Handle custom RAG exceptions."""
        try:
            logger.error(
                "RAG Exception",
                error_type=type(exc).__name__,
                error_message=str(exc),
                request_id=getattr(request.state, "request_id", None)
            )
        except:
            pass
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.error_type,
                "message": exc.message,
                "request_id": getattr(request.state, "request_id", None)
            }
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc: HTTPException):
        """Handle HTTP exceptions."""
        try:
            logger.warning(
                "HTTP Exception",
                status_code=exc.status_code,
                detail=exc.detail,
                request_id=getattr(request.state, "request_id", None)
            )
        except:
            pass
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": "HTTP Error",
                "message": exc.detail,
                "request_id": getattr(request.state, "request_id", None)
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc: Exception):
        """Handle unexpected exceptions."""
        try:
            logger.error(
                "Unexpected Exception",
                error_type=type(exc).__name__,
                error_message=str(exc),
                request_id=getattr(request.state, "request_id", None),
                exc_info=True
            )
        except:
            pass
        
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An unexpected error occurred",
                "request_id": getattr(request.state, "request_id", None)
            }
        )


# Create application instance
app = create_app()

if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.monitoring.log_level.lower()
    )
