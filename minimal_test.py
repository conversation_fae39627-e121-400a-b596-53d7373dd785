#!/usr/bin/env python3
"""
Minimal RAG test - tests core functionality without database dependencies.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def test_core_rag():
    """Test core RAG functionality."""
    print("Testing Core RAG Components...")
    print("=" * 40)
    
    # Test 1: Configuration
    print("1. Testing configuration...")
    try:
        from app.core.config import get_settings
        settings = get_settings()
        print(f"   [OK] Config loaded: {settings.app_name}")
    except Exception as e:
        print(f"   [ERROR] Config failed: {e}")
        return False
    
    # Test 2: Document Processor (standalone)
    print("\n2. Testing document processor...")
    try:
        from app.services.document_processor import DocumentProcessor
        processor = DocumentProcessor()
        print("   [OK] Document processor initialized")
        
        # Test text chunking
        sample_text = "This is a sample resume text. It contains multiple sentences and should be chunked properly for embeddings."
        chunks = processor.create_text_chunks(sample_text)
        print(f"   [OK] Text chunking: {len(chunks)} chunks created")
        
    except Exception as e:
        print(f"   [ERROR] Document processor failed: {e}")
        return False
    
    # Test 3: Check if OpenAI key is set
    print("\n3. Testing OpenAI configuration...")
    try:
        openai_key = settings.llm.openai_api_key
        if openai_key and openai_key != "your-openai-api-key-here":
            print("   [OK] OpenAI API key is configured")
            return True
        else:
            print("   [WARNING] OpenAI API key not set")
            print("   Please set OPENAI_API_KEY in .env file to test full pipeline")
            return True  # Still consider this OK for basic tests
    except Exception as e:
        print(f"   [ERROR] OpenAI config check failed: {e}")
        return False

def main():
    """Main test function."""
    success = test_core_rag()
    
    print("\n" + "=" * 40)
    if success:
        print("SUCCESS: Core components are working!")
        print("\nTo test the full RAG pipeline:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Set OPENAI_API_KEY in .env file")
        print("3. Run: python working_demo.py")
    else:
        print("FAILED: Please check the errors above")

if __name__ == "__main__":
    main()
