"""
Document processing tasks for Celery workers.
Handles file uploads, text extraction, and document storage.
"""

import asyncio
from typing import Dict, Any, List
from celery import current_task
import structlog

from app.workers.celery_app import celery_app
from app.services.document_processor import get_document_processor
from app.services.rag_engine import get_rag_engine
from app.core.exceptions import DocumentProcessingError
from app.core.logging import get_rag_logger

logger = get_rag_logger(__name__)


@celery_app.task(bind=True, name="process_candidate_documents")
def process_candidate_documents_task(
    self, 
    candidate_id: str, 
    documents_data: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Process candidate documents in background.
    
    Args:
        candidate_id: Candidate UUID
        documents_data: List of document data with file content
        
    Returns:
        dict: Processing results
    """
    task_id = current_task.request.id
    
    logger.info(
        "Starting document processing task",
        task_id=task_id,
        candidate_id=candidate_id,
        document_count=len(documents_data)
    )
    
    try:
        # Update task state
        self.update_state(
            state="PROCESSING",
            meta={
                "current_step": "initializing",
                "total_documents": len(documents_data),
                "processed_documents": 0
            }
        )
        
        # Get RAG engine
        rag_engine = get_rag_engine()
        
        # Process documents
        result = asyncio.run(
            rag_engine.process_and_store_candidate_documents(
                candidate_id=candidate_id,
                documents=documents_data
            )
        )
        
        # Update final state
        self.update_state(
            state="SUCCESS",
            meta={
                "current_step": "completed",
                "total_documents": len(documents_data),
                "processed_documents": len(documents_data),
                "result": result
            }
        )
        
        logger.info(
            "Document processing task completed",
            task_id=task_id,
            candidate_id=candidate_id,
            chunks_created=result.get("total_chunks_created", 0),
            processing_time=result.get("total_processing_time_seconds", 0)
        )
        
        return result
        
    except Exception as e:
        error_msg = f"Document processing failed: {str(e)}"
        
        logger.error(
            "Document processing task failed",
            task_id=task_id,
            candidate_id=candidate_id,
            error=error_msg,
            exc_info=True
        )
        
        # Update error state
        self.update_state(
            state="FAILURE",
            meta={
                "current_step": "failed",
                "error": error_msg,
                "error_type": type(e).__name__
            }
        )
        
        raise


@celery_app.task(bind=True, name="process_single_document")
def process_single_document_task(
    self,
    document_data: Dict[str, Any],
    candidate_id: str = None
) -> Dict[str, Any]:
    """
    Process a single document.
    
    Args:
        document_data: Document data with file content
        candidate_id: Optional candidate ID
        
    Returns:
        dict: Processing results
    """
    task_id = current_task.request.id
    filename = document_data.get("filename", "unknown")
    
    logger.info(
        "Starting single document processing",
        task_id=task_id,
        filename=filename,
        candidate_id=candidate_id
    )
    
    try:
        # Update task state
        self.update_state(
            state="PROCESSING",
            meta={
                "current_step": "extracting_text",
                "filename": filename
            }
        )
        
        # Get document processor
        processor = get_document_processor()
        
        # Process document
        result = asyncio.run(
            processor.process_document(
                file_content=document_data["file_content"],
                filename=document_data["filename"],
                document_type=document_data.get("document_type", "resume"),
                candidate_id=candidate_id
            )
        )
        
        logger.info(
            "Single document processing completed",
            task_id=task_id,
            filename=filename,
            chunks_created=result.get("chunks_created", 0),
            text_length=result.get("text_length", 0)
        )
        
        return result
        
    except Exception as e:
        error_msg = f"Single document processing failed: {str(e)}"
        
        logger.error(
            "Single document processing failed",
            task_id=task_id,
            filename=filename,
            error=error_msg,
            exc_info=True
        )
        
        self.update_state(
            state="FAILURE",
            meta={
                "current_step": "failed",
                "filename": filename,
                "error": error_msg
            }
        )
        
        raise


@celery_app.task(bind=True, name="reprocess_document")
def reprocess_document_task(
    self,
    document_id: str,
    force_reembedding: bool = False
) -> Dict[str, Any]:
    """
    Reprocess an existing document.
    
    Args:
        document_id: Document UUID
        force_reembedding: Whether to regenerate embeddings
        
    Returns:
        dict: Reprocessing results
    """
    task_id = current_task.request.id
    
    logger.info(
        "Starting document reprocessing",
        task_id=task_id,
        document_id=document_id,
        force_reembedding=force_reembedding
    )
    
    try:
        # TODO: Implement document reprocessing
        # This would:
        # 1. Load document from database
        # 2. Re-extract text if needed
        # 3. Regenerate chunks
        # 4. Update embeddings if force_reembedding=True
        # 5. Update vector store
        
        self.update_state(
            state="SUCCESS",
            meta={
                "current_step": "completed",
                "document_id": document_id
            }
        )
        
        result = {
            "document_id": document_id,
            "status": "reprocessed",
            "force_reembedding": force_reembedding
        }
        
        logger.info(
            "Document reprocessing completed",
            task_id=task_id,
            document_id=document_id
        )
        
        return result
        
    except Exception as e:
        error_msg = f"Document reprocessing failed: {str(e)}"
        
        logger.error(
            "Document reprocessing failed",
            task_id=task_id,
            document_id=document_id,
            error=error_msg,
            exc_info=True
        )
        
        self.update_state(
            state="FAILURE",
            meta={
                "current_step": "failed",
                "document_id": document_id,
                "error": error_msg
            }
        )
        
        raise


@celery_app.task(bind=True, name="bulk_document_processing")
def bulk_document_processing_task(
    self,
    document_batch: List[Dict[str, Any]],
    batch_name: str = "bulk_upload"
) -> Dict[str, Any]:
    """
    Process multiple documents in bulk.
    
    Args:
        document_batch: List of document data
        batch_name: Name for this batch
        
    Returns:
        dict: Bulk processing results
    """
    task_id = current_task.request.id
    total_docs = len(document_batch)
    
    logger.info(
        "Starting bulk document processing",
        task_id=task_id,
        batch_name=batch_name,
        total_documents=total_docs
    )
    
    try:
        results = []
        failed_documents = []
        
        for i, doc_data in enumerate(document_batch):
            try:
                # Update progress
                self.update_state(
                    state="PROCESSING",
                    meta={
                        "current_step": f"processing_document_{i+1}",
                        "current_document": doc_data.get("filename", f"doc_{i+1}"),
                        "progress": f"{i+1}/{total_docs}",
                        "completed": i,
                        "total": total_docs
                    }
                )
                
                # Process single document
                processor = get_document_processor()
                result = asyncio.run(
                    processor.process_document(
                        file_content=doc_data["file_content"],
                        filename=doc_data["filename"],
                        document_type=doc_data.get("document_type", "resume"),
                        candidate_id=doc_data.get("candidate_id")
                    )
                )
                
                results.append({
                    "filename": doc_data["filename"],
                    "status": "success",
                    "chunks_created": result.get("chunks_created", 0),
                    "processing_time": result.get("processing_time_seconds", 0)
                })
                
            except Exception as doc_error:
                error_info = {
                    "filename": doc_data.get("filename", f"doc_{i+1}"),
                    "status": "failed",
                    "error": str(doc_error)
                }
                failed_documents.append(error_info)
                results.append(error_info)
                
                logger.error(
                    "Document failed in bulk processing",
                    task_id=task_id,
                    filename=error_info["filename"],
                    error=str(doc_error)
                )
        
        # Final results
        final_result = {
            "batch_name": batch_name,
            "total_documents": total_docs,
            "successful_documents": total_docs - len(failed_documents),
            "failed_documents": len(failed_documents),
            "results": results,
            "failed_details": failed_documents
        }
        
        logger.info(
            "Bulk document processing completed",
            task_id=task_id,
            batch_name=batch_name,
            successful=final_result["successful_documents"],
            failed=final_result["failed_documents"]
        )
        
        return final_result
        
    except Exception as e:
        error_msg = f"Bulk document processing failed: {str(e)}"
        
        logger.error(
            "Bulk document processing failed",
            task_id=task_id,
            batch_name=batch_name,
            error=error_msg,
            exc_info=True
        )
        
        self.update_state(
            state="FAILURE",
            meta={
                "current_step": "failed",
                "batch_name": batch_name,
                "error": error_msg
            }
        )
        
        raise


@celery_app.task(name="validate_document_format")
def validate_document_format_task(file_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate document format before processing.
    
    Args:
        file_data: File data to validate
        
    Returns:
        dict: Validation results
    """
    try:
        processor = get_document_processor()
        
        # Validate file
        processor.validate_file(
            filename=file_data["filename"],
            file_size=len(file_data["file_content"])
        )
        
        return {
            "filename": file_data["filename"],
            "valid": True,
            "file_size": len(file_data["file_content"]),
            "file_type": file_data.get("file_type", "unknown")
        }
        
    except Exception as e:
        return {
            "filename": file_data.get("filename", "unknown"),
            "valid": False,
            "error": str(e)
        }
