#!/usr/bin/env python3
"""
Database initialization script for RAG System.
Handles database creation, migrations, and initial data setup.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import get_settings
from app.db.database import init_db, get_db_manager
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import structlog

# Setup logging
logger = structlog.get_logger(__name__)


async def create_database_if_not_exists():
    """Create the database if it doesn't exist."""
    settings = get_settings()
    
    # Parse database URL to get components
    db_url = settings.database.url
    if "postgresql://" in db_url:
        # Extract database name and server info
        parts = db_url.replace("postgresql://", "").split("/")
        if len(parts) >= 2:
            server_part = parts[0]
            db_name = parts[1]
            
            # Connect to postgres database (which always exists)
            postgres_url = f"postgresql+asyncpg://{server_part}/postgres"
            
            try:
                engine = create_async_engine(postgres_url)
                async with engine.connect() as conn:
                    # Check if database exists
                    result = await conn.execute(
                        text("SELECT 1 FROM pg_database WHERE datname=:db_name"),
                        {"db_name": db_name}
                    )
                    
                    if not result.fetchone():
                        # Create database
                        await conn.execute(text("COMMIT"))  # End any transaction
                        await conn.execute(text(f'CREATE DATABASE "{db_name}"'))
                        logger.info(f"Created database: {db_name}")
                    else:
                        logger.info(f"Database already exists: {db_name}")
                        
                await engine.dispose()
                
            except Exception as e:
                logger.warning(f"Could not create database automatically: {e}")
                logger.info("Please ensure your PostgreSQL database exists manually")


async def run_migrations():
    """Run Alembic migrations."""
    try:
        import subprocess
        import os
        
        # Change to project directory
        project_root = Path(__file__).parent.parent
        os.chdir(project_root)
        
        # Run alembic upgrade
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Database migrations completed successfully")
            return True
        else:
            logger.error(f"Migration failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to run migrations: {e}")
        return False


async def create_initial_data():
    """Create initial data for the system."""
    try:
        from app.models.user import User
        from app.models.email_template import EmailTemplate
        from sqlalchemy.ext.asyncio import AsyncSession
        from passlib.context import CryptContext
        import uuid
        
        # Password hashing
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Get database session
        db_manager = get_db_manager()
        
        async with db_manager.async_session_maker() as session:
            # Check if admin user exists
            admin_email = "<EMAIL>"
            existing_admin = await session.execute(
                text("SELECT id FROM users WHERE email = :email"),
                {"email": admin_email}
            )
            
            if not existing_admin.fetchone():
                # Create admin user
                admin_user = User(
                    id=uuid.uuid4(),
                    email=admin_email,
                    full_name="RAG System Administrator",
                    company="RAG Recruiter",
                    hashed_password=pwd_context.hash("admin123"),
                    is_active=True,
                    is_verified=True,
                    timezone="UTC"
                )
                session.add(admin_user)
                logger.info("Created admin user")
            
            # Create default email templates
            templates = [
                {
                    "name": "Professional Software Engineer Outreach",
                    "subject_template": "Exciting {role_title} opportunity at {company_name}",
                    "body_template": """Hi {candidate_name},

I hope this message finds you well! I came across your profile and was impressed by your background, particularly {personalization_evidence}.

We have an exciting {role_title} opportunity at {company_name} that seems like it could be a great fit for your skills and experience. {role_summary}

I'd love to schedule a brief 15-minute call to discuss this opportunity and see if it aligns with your career goals.

Would you be available for a quick conversation this week?

Best regards,
{recruiter_name}
""",
                    "template_type": "outreach",
                    "tone": "professional",
                    "industry": "technology",
                    "role_level": "mid",
                    "is_active": True,
                    "is_default": True
                },
                {
                    "name": "Senior Executive Outreach",
                    "subject_template": "Leadership opportunity: {role_title} at {company_name}",
                    "body_template": """Dear {candidate_name},

I trust you're doing well. Your impressive track record in {relevant_experience} has caught our attention, and I wanted to reach out regarding a strategic leadership opportunity.

{company_name} is seeking a {role_title} to {role_summary}. Given your background in {key_achievements}, I believe this could be an excellent next step in your career.

I'd appreciate the opportunity to discuss this confidential opportunity with you. Would you have 20 minutes available for a conversation this week?

I look forward to hearing from you.

Best regards,
{recruiter_name}
""",
                    "template_type": "outreach",
                    "tone": "executive",
                    "industry": "general",
                    "role_level": "senior",
                    "is_active": True,
                    "is_default": False
                }
            ]
            
            for template_data in templates:
                # Check if template exists
                existing_template = await session.execute(
                    text("SELECT id FROM email_templates WHERE name = :name"),
                    {"name": template_data["name"]}
                )
                
                if not existing_template.fetchone():
                    template = EmailTemplate(
                        id=uuid.uuid4(),
                        **template_data
                    )
                    session.add(template)
            
            await session.commit()
            logger.info("Created initial data")
            
    except Exception as e:
        logger.error(f"Failed to create initial data: {e}")
        return False
    
    return True


async def test_database_connection():
    """Test database connection and basic operations."""
    try:
        db_manager = get_db_manager()
        
        # Test basic connection
        health_ok = await db_manager.health_check()
        if not health_ok:
            logger.error("Database health check failed")
            return False
        
        # Test table exists
        async with db_manager.async_session_maker() as session:
            result = await session.execute(
                text("SELECT count(*) FROM information_schema.tables WHERE table_name = 'users'")
            )
            count = result.scalar()
            
            if count > 0:
                logger.info("Database tables verified")
                return True
            else:
                logger.error("Database tables not found")
                return False
                
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


async def main():
    """Main initialization function."""
    print("🗄️  RAG System Database Initialization")
    print("=" * 50)
    
    try:
        # Step 1: Create database if needed
        print("1. Checking/creating database...")
        await create_database_if_not_exists()
        
        # Step 2: Initialize database connection
        print("2. Initializing database connection...")
        await init_db()
        
        # Step 3: Run migrations
        print("3. Running database migrations...")
        migrations_ok = await run_migrations()
        if not migrations_ok:
            print("❌ Migration failed - stopping initialization")
            return False
        
        # Step 4: Create initial data
        print("4. Creating initial data...")
        initial_data_ok = await create_initial_data()
        
        # Step 5: Test everything
        print("5. Testing database connection...")
        test_ok = await test_database_connection()
        
        print("\n" + "=" * 50)
        if migrations_ok and test_ok:
            print("✅ Database initialization completed successfully!")
            print("\n📋 What was created:")
            print("   • All database tables and relationships")
            print("   • Admin user: <EMAIL> (password: admin123)")
            print("   • Default email templates")
            print("   • Database indexes and constraints")
            print("\n🚀 Next steps:")
            print("   • Change the admin password")
            print("   • Start the API server: python working_demo.py")
            print("   • Test the full pipeline")
            return True
        else:
            print("⚠️  Database initialization completed with warnings")
            print("Please check the logs above for any issues")
            return False
            
    except KeyboardInterrupt:
        print("\n⏹️  Initialization interrupted by user")
        return False
    except Exception as e:
        print(f"\n💥 Initialization failed: {e}")
        logger.error("Database initialization failed", error=str(e), exc_info=True)
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
