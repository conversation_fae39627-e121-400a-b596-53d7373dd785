# =============================================================================
# RAG System Makefile
# Common development and deployment tasks
# =============================================================================

.PHONY: help install dev test lint format clean build deploy docs

# Default target
help:
	@echo "RAG System - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  install     Install dependencies"
	@echo "  dev         Run development server"
	@echo "  test        Run test suite"
	@echo "  test-unit   Run unit tests only"
	@echo "  test-int    Run integration tests only"
	@echo "  lint        Run code linting"
	@echo "  format      Format code with black and isort"
	@echo "  clean       Clean project artifacts"
	@echo ""
	@echo "Database:"
	@echo "  db-init     Initialize database"
	@echo "  db-seed     Seed database with sample data"
	@echo "  db-reset    Reset database (WARNING: deletes all data)"
	@echo "  db-health   Check database health"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build    Build Docker image"
	@echo "  docker-dev      Run development environment with Docker"
	@echo "  docker-prod     Run production environment with Docker"
	@echo "  docker-down     Stop Docker services"
	@echo "  docker-clean    Clean Docker artifacts"
	@echo ""
	@echo "Deployment:"
	@echo "  build       Build for production"
	@echo "  deploy      Deploy to production"
	@echo "  docs        Generate documentation"

# =============================================================================
# Development Commands
# =============================================================================

install:
	@echo "📦 Installing dependencies..."
	pip install -r requirements.txt
	pre-commit install
	@echo "✅ Installation completed"

dev:
	@echo "🚀 Starting development server..."
	python scripts/dev.py serve

test:
	@echo "🧪 Running test suite..."
	python scripts/dev.py test

test-unit:
	@echo "🧪 Running unit tests..."
	pytest tests/unit/ -v

test-integration:
	@echo "🧪 Running integration tests..."
	pytest tests/integration/ tests/api/ -v

test-watch:
	@echo "👀 Running tests in watch mode..."
	ptw -- tests/

test-coverage:
	@echo "📊 Running tests with coverage..."
	pytest --cov=app --cov-report=html --cov-report=term-missing

lint:
	@echo "🔍 Running code linting..."
	python scripts/dev.py lint

format:
	@echo "🎨 Formatting code..."
	python scripts/dev.py format

clean:
	@echo "🧹 Cleaning project..."
	python scripts/dev.py clean

# =============================================================================
# Database Commands
# =============================================================================

db-init:
	@echo "🗄️  Initializing database..."
	python scripts/init_db.py init

db-seed:
	@echo "🌱 Seeding database..."
	python scripts/init_db.py seed

db-reset:
	@echo "⚠️  Resetting database..."
	python scripts/init_db.py reset

db-health:
	@echo "💓 Checking database health..."
	python scripts/init_db.py health

# =============================================================================
# Docker Commands
# =============================================================================

docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t rag-system:latest .

docker-dev:
	@echo "🐳 Starting development environment..."
	docker-compose up -d
	@echo "🚀 Services starting... Check status with: docker-compose ps"
	@echo "📖 API Documentation: http://localhost:8000/api/v1/docs"
	@echo "🔍 Health Check: http://localhost:8000/health"
	@echo "🌸 Flower (Celery): http://localhost:5555"
	@echo "📊 Grafana: http://localhost:3000 (admin/admin)"

docker-prod:
	@echo "🐳 Starting production environment..."
	docker-compose -f docker-compose.prod.yml up -d

docker-down:
	@echo "🐳 Stopping Docker services..."
	docker-compose down
	docker-compose -f docker-compose.prod.yml down

docker-logs:
	@echo "📋 Showing Docker logs..."
	docker-compose logs -f

docker-clean:
	@echo "🧹 Cleaning Docker artifacts..."
	docker system prune -f
	docker volume prune -f

# =============================================================================
# Development Helpers
# =============================================================================

setup-dev:
	@echo "🔧 Setting up development environment..."
	python scripts/dev.py setup
	cp .env.example .env
	@echo "⚠️  Please update .env with your API keys"

check-env:
	@echo "🔍 Checking environment configuration..."
	@python -c "from app.core.config import get_settings; print('✅ Configuration loaded successfully')"

# =============================================================================
# Testing Helpers
# =============================================================================

test-fast:
	@echo "⚡ Running fast tests only..."
	pytest -m "not slow" -x

test-slow:
	@echo "🐌 Running slow tests..."
	pytest -m "slow"

test-external:
	@echo "🌐 Running tests that require external services..."
	pytest -m "external"

# =============================================================================
# Code Quality
# =============================================================================

check-security:
	@echo "🔒 Running security checks..."
	bandit -r app/

check-dependencies:
	@echo "📦 Checking for dependency vulnerabilities..."
	safety check

pre-commit:
	@echo "🚀 Running pre-commit hooks..."
	pre-commit run --all-files

# =============================================================================
# Documentation
# =============================================================================

docs:
	@echo "📚 Generating documentation..."
	# TODO: Add documentation generation
	@echo "Documentation generation not implemented yet"

docs-serve:
	@echo "📖 Serving documentation locally..."
	# TODO: Add documentation serving
	@echo "Documentation serving not implemented yet"

# =============================================================================
# Production/Deployment
# =============================================================================

build:
	@echo "🏗️  Building for production..."
	docker build -t rag-system:latest --target production .

deploy:
	@echo "🚀 Deploying to production..."
	# TODO: Add deployment logic
	@echo "Deployment logic not implemented yet"

migrate:
	@echo "📊 Running database migrations..."
	alembic upgrade head

backup-db:
	@echo "💾 Backing up database..."
	# TODO: Add database backup logic
	@echo "Database backup not implemented yet"

# =============================================================================
# Monitoring
# =============================================================================

logs:
	@echo "📋 Showing application logs..."
	docker-compose logs -f api

monitor:
	@echo "📊 Opening monitoring dashboard..."
	open http://localhost:3000

health:
	@echo "💓 Checking system health..."
	curl -f http://localhost:8000/health || echo "❌ Health check failed"

# =============================================================================
# Utilities
# =============================================================================

shell:
	@echo "🐚 Opening Python shell with app context..."
	python -c "from app.main import app; from app.core.config import get_settings; print('App loaded. Settings:', get_settings().app_name)"

psql:
	@echo "🗄️  Opening PostgreSQL shell..."
	docker-compose exec postgres psql -U rag_user -d rag_db

redis-cli:
	@echo "📊 Opening Redis CLI..."
	docker-compose exec redis redis-cli
