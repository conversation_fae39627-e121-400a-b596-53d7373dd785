# =============================================================================
# RAG System .gitignore
# =============================================================================

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# =============================================================================
# Application Specific
# =============================================================================

# Environment configuration
.env
.env.local
.env.development
.env.test
.env.production
.env.prod

# Data directories
data/uploads/
data/chroma/
data/backups/
logs/
temp/

# Database
*.db
*.sqlite
*.sqlite3

# ChromaDB data
chroma_data/
vector_store/

# Uploaded files
uploads/
documents/
resumes/

# Temporary files
tmp/
temp/
*.tmp

# Log files
*.log
logs/

# =============================================================================
# Docker
# =============================================================================

# Docker volumes
docker-volumes/
.docker/

# =============================================================================
# OS Generated
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Security & Secrets
# =============================================================================

# API Keys and secrets
secrets/
.secrets
api_keys.txt
*.pem
*.key
*.crt
*.p12
*.keystore

# SSH keys
id_rsa
id_rsa.pub
id_ed25519
id_ed25519.pub

# =============================================================================
# Development Tools
# =============================================================================

# Backup files
*.bak
*.backup
*.old
*.orig

# Editor temporaries
*.swp
*.swo
*~

# =============================================================================
# Monitoring & Analytics
# =============================================================================

# Prometheus data
prometheus_data/

# Grafana data
grafana_data/

# Metrics
metrics/

# =============================================================================
# Documentation
# =============================================================================

# Generated docs
docs/_build/
docs/build/
site/

# =============================================================================
# Deployment
# =============================================================================

# Kubernetes
*.yaml.bak
kustomization.yaml

# Terraform
*.tfstate
*.tfstate.*
.terraform/
*.tfvars

# Helm
charts/*.tgz

# =============================================================================
# Misc
# =============================================================================

# Created structure script (temporary)
create_structure.py
