"""
Outreach email schemas for RAG-based email generation.
"""

from pydantic import BaseModel, EmailStr
from typing import List, Dict, Any, Optional
from uuid import UUID
from datetime import datetime


class EmailRequest(BaseModel):
    """Schema for email generation request."""
    candidate_id: UUID
    job_id: UUID
    company_name: str
    role_title: str
    recruiter_name: Optional[str] = None
    template_id: Optional[UUID] = None
    custom_instructions: Optional[str] = None
    tone: str = "professional"  # professional, friendly, casual
    include_salary: bool = False
    include_benefits: bool = False
    personalization_level: str = "high"  # low, medium, high


class EmailResponse(BaseModel):
    """Schema for generated email response."""
    id: UUID
    candidate_id: UUID
    job_id: UUID
    
    # Email content
    subject_line: str
    email_body: str
    call_to_action: str
    
    # Personalization details
    personalization_points: List[str]  # Specific details used
    evidence_used: List[str]  # Evidence snippets that informed content
    tone_analysis: Dict[str, float]  # Tone scores
    
    # Generation metadata
    word_count: int
    estimated_read_time_minutes: float
    personalization_score: float  # 0.0 to 1.0
    generation_time_seconds: float
    model_used: str
    template_id: Optional[UUID] = None
    
    created_at: datetime
    
    class Config:
        from_attributes = True


class BatchEmailRequest(BaseModel):
    """Schema for batch email generation."""
    candidate_ids: List[UUID]
    job_id: UUID
    company_name: str
    role_title: str
    recruiter_name: Optional[str] = None
    template_id: Optional[UUID] = None
    custom_instructions: Optional[str] = None
    tone: str = "professional"
    include_salary: bool = False
    include_benefits: bool = False
    personalization_level: str = "high"


class EmailTemplate(BaseModel):
    """Schema for email template."""
    id: UUID
    name: str
    description: str
    template_type: str  # outreach, follow_up, rejection, offer
    subject_template: str
    body_template: str
    variables: List[str]  # Available template variables
    tone: str
    is_active: bool
    created_by: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EmailTemplateCreate(BaseModel):
    """Schema for creating email template."""
    name: str
    description: str
    template_type: str
    subject_template: str
    body_template: str
    variables: List[str]
    tone: str = "professional"


class EmailTemplateUpdate(BaseModel):
    """Schema for updating email template."""
    name: Optional[str] = None
    description: Optional[str] = None
    subject_template: Optional[str] = None
    body_template: Optional[str] = None
    variables: Optional[List[str]] = None
    tone: Optional[str] = None
    is_active: Optional[bool] = None


class EmailHistory(BaseModel):
    """Schema for email generation history."""
    id: UUID
    candidate_name: str
    job_title: str
    company: str
    subject_line: str
    personalization_score: float
    generated_at: datetime


class EmailAnalytics(BaseModel):
    """Schema for email analytics."""
    total_emails_generated: int
    average_personalization_score: float
    most_used_templates: List[Dict[str, Any]]
    top_personalization_points: List[str]
    generation_time_stats: Dict[str, float]
    tone_distribution: Dict[str, int]


class ToneAnalysis(BaseModel):
    """Schema for email tone analysis."""
    overall_tone: str
    tone_scores: Dict[str, float]  # professional, friendly, urgent, etc.
    sentiment_score: float  # -1.0 to 1.0
    formality_level: float  # 0.0 to 1.0
    confidence_level: float
    suggestions: List[str]


class EmailSendRequest(BaseModel):
    """Schema for sending email (testing purposes)."""
    email_id: UUID
    recipient_email: EmailStr
    sender_name: str
    sender_email: EmailStr
    send_immediately: bool = False
    scheduled_time: Optional[datetime] = None


class EmailSendResponse(BaseModel):
    """Schema for email send confirmation."""
    email_id: UUID
    send_status: str  # sent, scheduled, failed
    message_id: Optional[str] = None
    sent_at: Optional[datetime] = None
    error_message: Optional[str] = None
