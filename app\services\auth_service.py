"""
Authentication service for user management and JWT token handling.
Provides secure login, registration, and session management.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import H<PERSON><PERSON>Ex<PERSON>, status
import structlog

from app.core.config import get_settings
from app.models.user import User
from app.schemas.auth import UserCreate, UserLogin, Token, UserResponse

logger = structlog.get_logger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """
    Authentication service for user management.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.secret_key = self.settings.security.secret_key
        self.algorithm = self.settings.security.algorithm
        self.access_token_expire_minutes = self.settings.security.access_token_expire_minutes
        self.refresh_token_expire_days = self.settings.security.refresh_token_expire_days
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Generate password hash."""
        return pwd_context.hash(password)
    
    def create_access_token(
        self, 
        data: dict, 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token."""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: dict) -> str:
        """Create JWT refresh token."""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> Optional[dict]:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type
            if payload.get("type") != token_type:
                return None
            
            # Check expiration
            if datetime.utcnow() > datetime.fromtimestamp(payload["exp"]):
                return None
            
            return payload
            
        except JWTError:
            return None
    
    async def get_user_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        """Get user by email address."""
        try:
            result = await db.execute(select(User).where(User.email == email))
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error("Failed to get user by email", email=email, error=str(e))
            return None
    
    async def get_user_by_id(self, db: AsyncSession, user_id: str) -> Optional[User]:
        """Get user by ID."""
        try:
            result = await db.execute(select(User).where(User.id == user_id))
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error("Failed to get user by ID", user_id=user_id, error=str(e))
            return None
    
    async def authenticate_user(
        self, 
        db: AsyncSession, 
        email: str, 
        password: str
    ) -> Optional[User]:
        """Authenticate user with email and password."""
        user = await self.get_user_by_email(db, email)
        
        if not user:
            logger.warning("Authentication failed - user not found", email=email)
            return None
        
        if not user.is_active:
            logger.warning("Authentication failed - user inactive", email=email)
            return None
        
        if not self.verify_password(password, user.hashed_password):
            logger.warning("Authentication failed - invalid password", email=email)
            return None
        
        # Update last login
        user.last_login = datetime.utcnow()
        await db.commit()
        
        logger.info("User authenticated successfully", email=email, user_id=str(user.id))
        return user
    
    async def create_user(
        self, 
        db: AsyncSession, 
        user_data: UserCreate
    ) -> User:
        """Create a new user account."""
        try:
            # Check if user already exists
            existing_user = await self.get_user_by_email(db, user_data.email)
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email already exists"
                )
            
            # Create new user
            hashed_password = self.get_password_hash(user_data.password)
            
            user = User(
                email=user_data.email,
                full_name=user_data.full_name,
                company=user_data.company,
                hashed_password=hashed_password,
                phone=user_data.phone,
                timezone=user_data.timezone or "UTC",
                is_active=True,
                is_verified=False  # Email verification required
            )
            
            db.add(user)
            await db.commit()
            await db.refresh(user)
            
            logger.info("User created successfully", email=user_data.email, user_id=str(user.id))
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            await db.rollback()
            logger.error("Failed to create user", email=user_data.email, error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user account"
            )
    
    async def login_user(
        self, 
        db: AsyncSession, 
        login_data: UserLogin
    ) -> Token:
        """Login user and return JWT tokens."""
        user = await self.authenticate_user(db, login_data.email, login_data.password)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create tokens
        access_token = self.create_access_token(
            data={"sub": str(user.id), "email": user.email}
        )
        refresh_token = self.create_refresh_token(
            data={"sub": str(user.id), "email": user.email}
        )
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=self.access_token_expire_minutes * 60
        )
    
    async def refresh_access_token(
        self, 
        db: AsyncSession, 
        refresh_token: str
    ) -> Token:
        """Refresh access token using refresh token."""
        payload = self.verify_token(refresh_token, "refresh")
        
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        user_id = payload.get("sub")
        user = await self.get_user_by_id(db, user_id)
        
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # Create new access token
        access_token = self.create_access_token(
            data={"sub": str(user.id), "email": user.email}
        )
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,  # Keep same refresh token
            token_type="bearer",
            expires_in=self.access_token_expire_minutes * 60
        )
    
    async def get_current_user(
        self, 
        db: AsyncSession, 
        token: str
    ) -> User:
        """Get current user from JWT token."""
        payload = self.verify_token(token, "access")
        
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user_id = payload.get("sub")
        user = await self.get_user_by_id(db, user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is inactive"
            )
        
        return user


# Global service instance
_auth_service: Optional[AuthService] = None


def get_auth_service() -> AuthService:
    """
    Get or create authentication service instance.
    
    Returns:
        AuthService: Service instance
    """
    global _auth_service
    
    if _auth_service is None:
        _auth_service = AuthService()
    
    return _auth_service
