#!/bin/bash

# =============================================================================
# RAG System Quick Start Script
# Sets up and runs the recruitment RAG system
# =============================================================================

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}╔══════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${NC}               ${CYAN}RAG System Quick Start${NC}               ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}        Intelligent Recruitment Platform         ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # Check Python (optional, for direct development)
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        print_status "Python $PYTHON_VERSION found"
    else
        print_warning "Python 3 not found. Docker will be used for development."
    fi
    
    print_success "Prerequisites check completed"
}

# Setup environment
setup_environment() {
    print_status "Setting up environment configuration..."
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
        else
            print_error ".env.example file not found!"
            exit 1
        fi
    else
        print_status ".env file already exists"
    fi
    
    # Check if OpenAI API key is set
    if grep -q "your-openai-api-key-here" .env; then
        print_warning "OpenAI API key not configured!"
        echo ""
        echo "Please set your OpenAI API key in the .env file:"
        echo "1. Get your API key from: https://platform.openai.com/api-keys"
        echo "2. Edit .env file and replace 'your-openai-api-key-here' with your actual key"
        echo ""
        read -p "Press Enter to continue after setting your API key..."
    fi
}

# Build and start services
start_services() {
    print_status "Building and starting RAG System services..."
    
    # Pull latest images
    print_status "Pulling Docker images..."
    docker-compose pull
    
    # Build the application
    print_status "Building RAG System..."
    docker-compose build
    
    # Start services
    print_status "Starting services..."
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to start..."
    sleep 10
    
    # Check service health
    check_services_health
}

# Check service health
check_services_health() {
    print_status "Checking service health..."
    
    # Check PostgreSQL
    if docker-compose exec -T postgres pg_isready -U rag_user > /dev/null 2>&1; then
        print_success "PostgreSQL is ready"
    else
        print_error "PostgreSQL is not ready"
        return 1
    fi
    
    # Check Redis
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is ready"
    else
        print_error "Redis is not ready"
        return 1
    fi
    
    # Check ChromaDB
    if curl -f http://localhost:8001/api/v1/heartbeat > /dev/null 2>&1; then
        print_success "ChromaDB is ready"
    else
        print_warning "ChromaDB may still be starting..."
    fi
    
    # Check API
    sleep 5  # Give API more time to start
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "RAG API is ready"
    else
        print_warning "RAG API may still be starting..."
        print_status "Waiting for API to be ready..."
        for i in {1..30}; do
            if curl -f http://localhost:8000/health > /dev/null 2>&1; then
                print_success "RAG API is now ready"
                break
            fi
            sleep 2
            echo -n "."
        done
        echo ""
    fi
}

# Initialize database
initialize_database() {
    print_status "Initializing database..."
    
    # Wait a bit more for database to be fully ready
    sleep 5
    
    # Run database initialization
    if docker-compose exec -T api python scripts/init_db.py init; then
        print_success "Database initialized successfully"
    else
        print_error "Failed to initialize database"
        return 1
    fi
}

# Show access information
show_access_info() {
    echo ""
    print_success "🎉 RAG System is now running!"
    echo ""
    echo -e "${CYAN}📍 Access Points:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "🌐 API Server:        ${GREEN}http://localhost:8000${NC}"
    echo -e "📖 API Documentation: ${GREEN}http://localhost:8000/api/v1/docs${NC}"
    echo -e "🔍 Health Check:      ${GREEN}http://localhost:8000/health${NC}"
    echo -e "🌸 Celery Monitor:    ${GREEN}http://localhost:5555${NC}"
    echo -e "📊 Grafana Dashboard: ${GREEN}http://localhost:3000${NC} (admin/admin)"
    echo -e "📈 Prometheus:        ${GREEN}http://localhost:9090${NC}"
    echo ""
    echo -e "${CYAN}🔧 Management Commands:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "View logs:           docker-compose logs -f"
    echo "Stop services:       docker-compose down"
    echo "Restart services:    docker-compose restart"
    echo "Run tests:           docker-compose exec api pytest"
    echo "Access database:     docker-compose exec postgres psql -U rag_user -d rag_db"
    echo "Access Redis:        docker-compose exec redis redis-cli"
    echo ""
    echo -e "${CYAN}📚 Next Steps:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "1. Visit the API documentation to explore endpoints"
    echo "2. Upload candidate resumes via /api/v1/documents/upload"
    echo "3. Create job postings via /api/v1/jobs/"
    echo "4. Score candidates against jobs via /api/v1/scoring/score"
    echo "5. Generate outreach emails via /api/v1/outreach/generate-email"
    echo ""
}

# Clean up on failure
cleanup_on_failure() {
    print_error "Setup failed. Cleaning up..."
    docker-compose down
    exit 1
}

# Main setup function
main() {
    print_header
    
    # Set up error handling
    trap cleanup_on_failure ERR
    
    # Run setup steps
    check_prerequisites
    setup_environment
    start_services
    initialize_database
    show_access_info
    
    print_success "Setup completed successfully! 🚀"
}

# Run main function
main "$@"
