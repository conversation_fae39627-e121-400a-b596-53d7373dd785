#!/usr/bin/env python3
"""
Simple test script to verify the RAG pipeline is working end-to-end.
Tests document upload, processing, vector storage, and scoring.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from app.services.rag_engine import get_rag_engine
from app.core.config import get_settings


async def test_rag_pipeline():
    """Test the complete RAG pipeline."""
    
    print("🚀 Testing RAG Pipeline...")
    print("=" * 50)
    
    # Test 1: Configuration and service initialization
    print("1. Testing configuration and service initialization...")
    try:
        settings = get_settings()
        print(f"   ✅ Settings loaded: {settings.app_name}")
        
        rag_engine = get_rag_engine()
        print("   ✅ RAG Engine initialized")
        
    except Exception as e:
        print(f"   ❌ Configuration failed: {e}")
        return False
    
    # Test 2: Health checks
    print("\n2. Testing service health checks...")
    try:
        health_status = await rag_engine.health_check()
        print(f"   📊 Overall health: {health_status['rag_engine']}")
        
        for component, status in health_status['components'].items():
            status_emoji = "✅" if status['status'] == 'healthy' else "❌"
            print(f"   {status_emoji} {component}: {status['status']}")
            
        if health_status['rag_engine'] != 'healthy':
            print(f"   ⚠️  System not fully healthy: {health_status}")
            
    except Exception as e:
        print(f"   ❌ Health check failed: {e}")
        return False
    
    # Test 3: Document processing with sample text
    print("\n3. Testing document processing...")
    try:
        # Create a sample resume content
        sample_resume = """
        John Doe
        Senior Python Developer
        
        Experience:
        - 5 years of Python development experience
        - Expertise in FastAPI, Django, and Flask frameworks
        - Experience with machine learning using scikit-learn and TensorFlow
        - Proficient in PostgreSQL, MongoDB, and Redis
        - Strong background in cloud platforms (AWS, Docker)
        
        Education:
        - Bachelor's in Computer Science from MIT
        - AWS Certified Solutions Architect
        
        Projects:
        - Built a recommendation system serving 1M+ users
        - Developed microservices architecture handling 10K+ requests/second
        - Led a team of 5 developers on e-commerce platform
        """.strip()
        
        # Process the document
        documents_data = [{
            "file_content": sample_resume.encode('utf-8'),
            "filename": "john_doe_resume.txt",
            "document_type": "resume"
        }]
        
        candidate_id = "test-candidate-123"
        
        result = await rag_engine.process_and_store_candidate_documents(
            candidate_id=candidate_id,
            documents=documents_data
        )
        
        print(f"   ✅ Document processed: {result['documents_processed']} documents")
        print(f"   📄 Chunks created: {result['total_chunks_created']}")
        print(f"   ⏱️  Processing time: {result['total_processing_time_seconds']:.2f}s")
        
    except Exception as e:
        print(f"   ❌ Document processing failed: {e}")
        return False
    
    # Test 4: Candidate scoring
    print("\n4. Testing candidate scoring...")
    try:
        job_description = """
        Senior Python Developer Position
        
        We are looking for an experienced Python developer to join our team.
        
        Requirements:
        - 3+ years of Python development experience
        - Experience with web frameworks (FastAPI, Django, Flask)
        - Knowledge of databases (PostgreSQL, MongoDB)
        - Experience with cloud platforms and containerization
        - Machine learning experience is a plus
        - Strong problem-solving skills and team collaboration
        """
        
        scoring_result = await rag_engine.score_candidate(
            candidate_id=candidate_id,
            job_description=job_description,
            ideal_candidate_profile="Senior developer with 5+ years experience and ML background"
        )
        
        score_data = scoring_result['scoring_data']
        print(f"   ✅ Scoring completed")
        print(f"   📊 Overall score: {score_data.get('overall_score', 0):.2f}")
        print(f"   📈 Percentage: {score_data.get('overall_percentage', 0)}%")
        print(f"   🎯 Fit level: {score_data.get('fit_level', 'unknown')}")
        print(f"   📝 Evidence chunks used: {scoring_result['total_evidence_chunks']}")
        
    except Exception as e:
        print(f"   ❌ Candidate scoring failed: {e}")
        return False
    
    # Test 5: Email generation
    print("\n5. Testing email generation...")
    try:
        email_result = await rag_engine.generate_outreach_email(
            candidate_id=candidate_id,
            company_name="TechCorp",
            role_title="Senior Python Developer",
            recruiter_name="Sarah Smith",
            tone="professional"
        )
        
        email_content = email_result['email_content']
        print(f"   ✅ Email generated")
        print(f"   📧 Subject: {email_content.get('subject_line', 'N/A')}")
        print(f"   📄 Email length: {len(email_content.get('email_body', ''))} characters")
        print(f"   🎯 Personalization points: {email_result['personalization_metadata']['evidence_chunks_used']}")
        
    except Exception as e:
        print(f"   ❌ Email generation failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 RAG Pipeline Test Completed Successfully!")
    print("All core components are working correctly.")
    
    return True


async def main():
    """Main test function."""
    try:
        success = await test_rag_pipeline()
        if success:
            print("\n✨ Your RAG system is ready for use!")
            print("You can now start the API server with: uvicorn app.main:app --reload")
        else:
            print("\n💥 Some components failed. Please check the error messages above.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error during testing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
