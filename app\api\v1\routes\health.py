"""
Health check and system status endpoints.
Provides comprehensive system health monitoring.
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import time
from typing import Dict, Any

from app.db.database import get_db, get_db_manager
from app.core.config import get_settings
from app.services.vector_store import get_vector_store
from app.services.llm_service import get_llm_service

router = APIRouter()


@router.get("/")
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    
    Returns:
        dict: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "RAG System"
    }


@router.get("/detailed")
async def detailed_health_check(
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Detailed health check including all system components.
    
    Args:
        db: Database session
        
    Returns:
        dict: Comprehensive health status
    """
    settings = get_settings()
    db_manager = get_db_manager()
    
    health_status = {
        "status": "healthy",
        "timestamp": time.time(),
        "service": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment,
        "components": {}
    }
    
    # Check database health
    try:
        db_healthy = await db_manager.health_check()
        health_status["components"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "type": "postgresql"
        }
    except Exception as e:
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "type": "postgresql"
        }
    
    # Check vector store health
    try:
        vector_store = get_vector_store()
        vector_healthy = await vector_store.health_check()
        health_status["components"]["vector_store"] = {
            "status": "healthy" if vector_healthy else "unhealthy",
            "type": "chromadb"
        }
    except Exception as e:
        health_status["components"]["vector_store"] = {
            "status": "unhealthy", 
            "error": str(e),
            "type": "chromadb"
        }
    
    # Check LLM service health
    try:
        llm_service = get_llm_service()
        llm_healthy = await llm_service.health_check()
        health_status["components"]["llm_service"] = {
            "status": "healthy" if llm_healthy else "unhealthy",
            "provider": settings.llm.openai_model
        }
    except Exception as e:
        health_status["components"]["llm_service"] = {
            "status": "unhealthy",
            "error": str(e),
            "provider": settings.llm.openai_model
        }
    
    # Determine overall health
    component_statuses = [
        comp["status"] for comp in health_status["components"].values()
    ]
    
    if all(status == "healthy" for status in component_statuses):
        health_status["status"] = "healthy"
    elif any(status == "healthy" for status in component_statuses):
        health_status["status"] = "degraded"
    else:
        health_status["status"] = "unhealthy"
    
    return health_status


@router.get("/readiness")
async def readiness_check(
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Readiness check for Kubernetes deployment.
    
    Args:
        db: Database session
        
    Returns:
        dict: Readiness status
    """
    try:
        # Quick database check
        db_manager = get_db_manager()
        db_healthy = await db_manager.health_check()
        
        if db_healthy:
            return {"status": "ready"}
        else:
            return {"status": "not_ready", "reason": "database_unavailable"}
            
    except Exception as e:
        return {"status": "not_ready", "reason": str(e)}


@router.get("/liveness")
async def liveness_check() -> Dict[str, Any]:
    """
    Liveness check for Kubernetes deployment.
    
    Returns:
        dict: Liveness status
    """
    return {
        "status": "alive",
        "timestamp": time.time()
    }
