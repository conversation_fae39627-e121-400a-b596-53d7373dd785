@echo off
echo 🚀 RAG System Quick Start for Windows
echo.

REM Install minimal dependencies
echo 📦 Installing dependencies...
pip install fastapi uvicorn pydantic pydantic-settings python-dotenv

REM Create .env file if it doesn't exist
if not exist .env (
    echo 📝 Creating .env file...
    copy .env.example .env
)

REM Create data directories
if not exist data mkdir data
if not exist data\uploads mkdir data\uploads
if not exist data\chroma mkdir data\chroma

echo.
echo ✅ Setup complete!
echo.
echo 📍 Opening browser to API documentation...
echo 🌐 API Documentation: http://localhost:8000/api/v1/docs
echo 🔍 Health Check: http://localhost:8000/health
echo.
echo Press Ctrl+C to stop the server
echo ================================================
echo.

REM Start the server
python minimal_server.py

pause
