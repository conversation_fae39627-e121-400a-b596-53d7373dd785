# Deployment Guide

This guide covers deploying the RAG System to various environments.

## 📋 Prerequisites

- Docker and Docker Compose
- PostgreSQL 15+ (for production)
- Redis 7+ (for task queue)
- OpenAI API key
- SSL certificates (for production)

## 🚀 Quick Deployment Options

### Local Development

```bash
# 1. Clone and setup
git clone <repository>
cd RAG
cp .env.example .env

# 2. Configure environment
# Edit .env with your OpenAI API key

# 3. Start services
make docker-dev

# 4. Initialize database
make db-init

# 5. Access the application
# API: http://localhost:8000
# Docs: http://localhost:8000/api/v1/docs
# Monitoring: http://localhost:3000
```

### Production Deployment

```bash
# 1. Prepare environment
cp .env.example .env.prod
# Configure production settings in .env.prod

# 2. Deploy
make docker-prod

# 3. Initialize database
make db-init

# 4. Configure reverse proxy (Nginx)
# See nginx configuration below
```

## 🔧 Environment Configuration

### Required Environment Variables

```bash
# API Keys
OPENAI_API_KEY=your-openai-api-key

# Database
DATABASE_URL=********************************/db
POSTGRES_DB=rag_db
POSTGRES_USER=rag_user
POSTGRES_PASSWORD=secure_password

# Redis
REDIS_URL=redis://redis:6379/0

# Security
SECRET_KEY=your-super-secret-key-64-chars-minimum
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com

# ChromaDB
CHROMA_PERSIST_DIRECTORY=/app/data/chroma

# Monitoring
GRAFANA_ADMIN_PASSWORD=secure_password
GRAFANA_SECRET_KEY=another-secret-key
```

### Optional Configuration

```bash
# LLM Settings
OPENAI_MODEL=gpt-4-1106-preview
OPENAI_EMBEDDING_MODEL=text-embedding-3-large
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.1

# File Processing
MAX_FILE_SIZE_MB=50
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Performance
TOP_K_RETRIEVAL=5
SIMILARITY_THRESHOLD=0.7

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
SENTRY_DSN=your-sentry-dsn
```

## ☁️ Cloud Platform Deployment

### AWS ECS

1. **Build and push Docker image:**
```bash
docker build -t rag-system:latest .
docker tag rag-system:latest your-account.dkr.ecr.region.amazonaws.com/rag-system:latest
docker push your-account.dkr.ecr.region.amazonaws.com/rag-system:latest
```

2. **Create ECS task definition:**
```json
{
  "family": "rag-system",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "containerDefinitions": [
    {
      "name": "rag-api",
      "image": "your-account.dkr.ecr.region.amazonaws.com/rag-system:latest",
      "portMappings": [{"containerPort": 8000}],
      "environment": [
        {"name": "DATABASE_URL", "value": "postgresql://..."},
        {"name": "OPENAI_API_KEY", "value": "..."}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/rag-system",
          "awslogs-region": "us-west-2"
        }
      }
    }
  ]
}
```

3. **Create ECS service with load balancer**

### Google Cloud Run

```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT-ID/rag-system
gcloud run deploy rag-system \
  --image gcr.io/PROJECT-ID/rag-system \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars OPENAI_API_KEY=your-key,DATABASE_URL=postgresql://...
```

### Digital Ocean App Platform

```yaml
# app.yaml
name: rag-system
services:
- name: api
  source_dir: /
  github:
    repo: your-username/rag-system
    branch: main
  run_command: uvicorn app.main:app --host 0.0.0.0 --port 8080
  environment_slug: docker
  instance_count: 2
  instance_size_slug: professional-s
  envs:
  - key: OPENAI_API_KEY
    value: your-key
    type: SECRET
  - key: DATABASE_URL
    value: postgresql://...
    type: SECRET
```

## 🗄️ Database Setup

### PostgreSQL (Recommended)

```bash
# Create database
createdb rag_db

# Run migrations
alembic upgrade head

# Seed initial data (optional)
python scripts/init_db.py seed
```

### Backup Strategy

```bash
# Daily backup script
#!/bin/bash
pg_dump $DATABASE_URL | gzip > backups/rag_db_$(date +%Y%m%d).sql.gz

# Retention policy (keep 30 days)
find backups/ -name "*.sql.gz" -mtime +30 -delete
```

## 🔐 Security Configuration

### SSL/TLS Setup

```nginx
server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### API Rate Limiting

```python
# In production settings
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10
```

### Secrets Management

Use proper secrets management:
- AWS Secrets Manager
- Google Secret Manager
- HashiCorp Vault
- Kubernetes Secrets

## 📊 Monitoring & Observability

### Health Checks

```bash
# Kubernetes liveness probe
livenessProbe:
  httpGet:
    path: /health/liveness
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10

# Kubernetes readiness probe
readinessProbe:
  httpGet:
    path: /health/readiness
    port: 8000
  initialDelaySeconds: 5
  periodSeconds: 5
```

### Metrics Collection

The system exposes Prometheus metrics at `/metrics`:
- Request latency
- Request count by endpoint
- Error rates
- LLM usage metrics
- Vector search performance

### Logging

Structured JSON logging is enabled in production:
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "message": "Candidate scoring completed",
  "candidate_id": "123e4567-e89b-12d3-a456-426614174000",
  "overall_score": 0.85,
  "processing_time": 2.5
}
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy RAG System
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Run tests
      run: |
        make install
        make test
        make lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - name: Deploy to production
      run: |
        make build
        make deploy
```

## 📈 Scaling Considerations

### Horizontal Scaling

- API: Scale to 4-8 instances behind load balancer
- Workers: Scale Celery workers based on queue length
- Database: Use read replicas for read-heavy workloads

### Performance Optimization

- Enable Redis caching for embeddings
- Use connection pooling for database
- Implement request caching for repeated queries
- Consider GPU instances for heavy LLM workloads

### Resource Requirements

| Component | CPU | Memory | Storage |
|-----------|-----|--------|---------|
| API (per instance) | 1 core | 1GB | 10GB |
| Worker (per instance) | 1 core | 1GB | 10GB |
| PostgreSQL | 2 cores | 4GB | 100GB |
| ChromaDB | 1 core | 2GB | 50GB |
| Redis | 0.5 cores | 1GB | 10GB |

## 🆘 Troubleshooting

### Common Issues

1. **Database connection errors:**
   ```bash
   # Check connection
   make db-health
   
   # Reset connection pool
   docker-compose restart api
   ```

2. **OpenAI API errors:**
   ```bash
   # Verify API key
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
        https://api.openai.com/v1/models
   ```

3. **Vector store issues:**
   ```bash
   # Check ChromaDB health
   curl http://localhost:8001/api/v1/heartbeat
   
   # Reset vector database
   docker-compose restart chroma
   ```

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
export DEBUG=true

# Run with debug output
make dev
```

## 📞 Support

For deployment support:
1. Check the logs: `make logs`
2. Review health status: `make health`
3. Check the troubleshooting guide above
4. Contact support with error details
