"""
RAG Engine - Core orchestration service for Retrieval-Augmented Generation.
Coordinates LLM, vector store, and document processing for candidate evaluation.
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from uuid import UUID

from app.services.llm_service import get_llm_service
from app.services.vector_store import get_vector_store
from app.services.document_processor import get_document_processor
from app.core.config import get_settings
from app.core.exceptions import RAGException, LLMError, VectorStoreError
from app.core.logging import get_rag_logger

logger = get_rag_logger(__name__)


class RAGEngine:
    """
    Main RAG engine that orchestrates retrieval and generation for candidate scoring.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.llm_service = get_llm_service()
        self.vector_store = get_vector_store()
        self.document_processor = get_document_processor()
    
    async def process_and_store_candidate_documents(
        self,
        candidate_id: str,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Process candidate documents and store in vector database.
        
        Args:
            candidate_id: Candidate UUID
            documents: List of document data (file_content, filename, type)
            
        Returns:
            dict: Processing results and statistics
        """
        start_time = time.time()
        processed_docs = []
        total_chunks = 0
        
        try:
            for doc_data in documents:
                # Process document
                processing_result = await self.document_processor.process_document(
                    file_content=doc_data["file_content"],
                    filename=doc_data["filename"],
                    document_type=doc_data.get("document_type", "resume"),
                    candidate_id=candidate_id
                )
                
                # Generate embeddings for chunks
                chunk_texts = [chunk["content"] for chunk in processing_result["chunks"]]
                
                if chunk_texts:
                    embeddings = await self.llm_service.generate_embeddings(chunk_texts)
                    
                    # Store in vector database
                    await self.vector_store.add_candidate_documents(
                        candidate_id=candidate_id,
                        documents=processing_result["chunks"],
                        embeddings=embeddings
                    )
                    
                    total_chunks += len(processing_result["chunks"])
                
                processed_docs.append({
                    "filename": doc_data["filename"],
                    "file_path": processing_result["file_path"],
                    "chunks_created": processing_result["chunks_created"],
                    "text_length": processing_result["text_length"],
                    "processing_time": processing_result["processing_time_seconds"]
                })
            
            total_time = time.time() - start_time
            
            result = {
                "candidate_id": candidate_id,
                "documents_processed": len(documents),
                "total_chunks_created": total_chunks,
                "processed_documents": processed_docs,
                "total_processing_time_seconds": total_time,
                "status": "completed"
            }
            
            logger.info(
                "Candidate documents processed and stored",
                candidate_id=candidate_id,
                documents_count=len(documents),
                chunks_count=total_chunks,
                processing_time=total_time
            )
            
            return result
            
        except Exception as e:
            total_time = time.time() - start_time
            error_msg = f"Failed to process candidate documents: {str(e)}"
            
            logger.error(
                "Document processing failed",
                candidate_id=candidate_id,
                documents_count=len(documents),
                processing_time=total_time,
                error=error_msg
            )
            
            raise RAGException(error_msg)
    
    async def retrieve_candidate_evidence(
        self,
        query_text: str,
        candidate_id: Optional[str] = None,
        top_k: int = None,
        similarity_threshold: float = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant evidence for a query using semantic search.
        
        Args:
            query_text: Search query
            candidate_id: Optional candidate filter
            top_k: Number of results to return
            similarity_threshold: Minimum similarity score
            
        Returns:
            List[Dict]: Retrieved evidence chunks
        """
        start_time = time.time()
        
        try:
            # Use configured defaults if not provided
            top_k = top_k or self.settings.processing.top_k_retrieval
            similarity_threshold = similarity_threshold or self.settings.processing.similarity_threshold
            
            # Generate query embedding
            query_embeddings = await self.llm_service.generate_embeddings([query_text])
            query_embedding = query_embeddings[0]
            
            # Search vector store
            results = await self.vector_store.search_candidate_evidence(
                query_embedding=query_embedding,
                candidate_id=candidate_id,
                top_k=top_k,
                similarity_threshold=similarity_threshold
            )
            
            search_time = time.time() - start_time
            
            logger.log_vector_search(
                query=query_text,
                results_count=len(results),
                search_time=search_time,
                similarity_threshold=similarity_threshold
            )
            
            return results
            
        except Exception as e:
            search_time = time.time() - start_time
            error_msg = f"Evidence retrieval failed: {str(e)}"
            
            logger.log_vector_search(
                query=query_text,
                results_count=0,
                search_time=search_time,
                error=error_msg
            )
            
            raise RAGException(error_msg)
    
    async def score_candidate(
        self,
        candidate_id: str,
        job_description: str,
        ideal_candidate_profile: Optional[str] = None,
        custom_requirements: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Score a candidate against job requirements using RAG.
        
        Args:
            candidate_id: Candidate UUID
            job_description: Job description text
            ideal_candidate_profile: Optional ideal candidate description
            custom_requirements: Optional custom requirements list
            
        Returns:
            dict: Structured candidate scoring result
        """
        start_time = time.time()
        
        try:
            # Create search query from job description and requirements
            search_queries = [job_description]
            
            if ideal_candidate_profile:
                search_queries.append(ideal_candidate_profile)
            
            if custom_requirements:
                search_queries.extend(custom_requirements)
            
            # Retrieve evidence for all queries
            all_evidence = []
            
            for query in search_queries:
                evidence = await self.retrieve_candidate_evidence(
                    query_text=query,
                    candidate_id=candidate_id,
                    top_k=self.settings.processing.top_k_retrieval
                )
                all_evidence.extend(evidence)
            
            # Remove duplicates and sort by similarity
            unique_evidence = {}
            for item in all_evidence:
                if item["id"] not in unique_evidence:
                    unique_evidence[item["id"]] = item
                elif item["similarity_score"] > unique_evidence[item["id"]]["similarity_score"]:
                    unique_evidence[item["id"]] = item
            
            sorted_evidence = sorted(
                unique_evidence.values(),
                key=lambda x: x["similarity_score"],
                reverse=True
            )
            
            # Prepare evidence snippets for LLM
            evidence_snippets = [item["content"] for item in sorted_evidence[:10]]
            
            # Generate candidate information summary
            candidate_data = f"Candidate ID: {candidate_id}"
            if sorted_evidence:
                candidate_data += f"\n\nKey information found in {len(evidence_snippets)} document sections."
            
            # Generate scoring using LLM
            llm_result = await self.llm_service.generate_candidate_score(
                job_description=job_description,
                candidate_data=candidate_data,
                evidence_snippets=evidence_snippets,
                ideal_profile=ideal_candidate_profile
            )
            
            # Parse LLM response (assuming JSON format)
            try:
                scoring_data = json.loads(llm_result["text"])
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                scoring_data = {
                    "candidate_name": "Unknown",
                    "overall_score": 0.5,
                    "overall_percentage": 50,
                    "fit_level": "fair",
                    "requirements": [],
                    "notable_strengths": ["Analysis completed"],
                    "potential_gaps": ["Unable to parse detailed results"],
                    "reasoning_summary": llm_result["text"]
                }
            
            total_time = time.time() - start_time
            
            # Compile final result
            result = {
                "candidate_id": candidate_id,
                "scoring_data": scoring_data,
                "evidence_used": evidence_snippets,
                "total_evidence_chunks": len(sorted_evidence),
                "processing_metadata": {
                    "processing_time_seconds": total_time,
                    "model_used": llm_result.get("model", "unknown"),
                    "llm_tokens_used": llm_result.get("usage", {}).get("total_tokens", 0),
                    "evidence_retrieval_count": len(all_evidence),
                    "unique_evidence_count": len(unique_evidence)
                }
            }
            
            logger.log_candidate_scoring(
                candidate_id=candidate_id,
                job_id="unknown",  # Would be passed in real implementation
                overall_score=scoring_data.get("overall_score", 0.0),
                evidence_count=len(evidence_snippets),
                processing_time=total_time
            )
            
            return result
            
        except Exception as e:
            total_time = time.time() - start_time
            error_msg = f"Candidate scoring failed: {str(e)}"
            
            logger.log_candidate_scoring(
                candidate_id=candidate_id,
                job_id="unknown",
                overall_score=0.0,
                evidence_count=0,
                processing_time=total_time,
                error=error_msg
            )
            
            raise RAGException(error_msg)
    
    async def generate_outreach_email(
        self,
        candidate_id: str,
        company_name: str,
        role_title: str,
        recruiter_name: Optional[str] = None,
        tone: str = "professional"
    ) -> Dict[str, Any]:
        """
        Generate personalized outreach email using RAG.
        
        Args:
            candidate_id: Candidate UUID
            company_name: Company name
            role_title: Role title
            recruiter_name: Recruiter's name
            tone: Email tone
            
        Returns:
            dict: Generated email content with personalization details
        """
        start_time = time.time()
        
        try:
            # Retrieve candidate evidence for personalization
            personalization_queries = [
                f"projects achievements skills experience {role_title}",
                "leadership accomplishments awards recognition",
                "technical skills programming languages frameworks",
                "education certifications training"
            ]
            
            all_evidence = []
            for query in personalization_queries:
                evidence = await self.retrieve_candidate_evidence(
                    query_text=query,
                    candidate_id=candidate_id,
                    top_k=3  # Fewer results for email personalization
                )
                all_evidence.extend(evidence)
            
            # Get best evidence for personalization
            unique_evidence = {}
            for item in all_evidence:
                if item["id"] not in unique_evidence:
                    unique_evidence[item["id"]] = item
            
            sorted_evidence = sorted(
                unique_evidence.values(),
                key=lambda x: x["similarity_score"],
                reverse=True
            )
            
            evidence_snippets = [item["content"] for item in sorted_evidence[:5]]
            
            # Generate email using LLM
            llm_result = await self.llm_service.generate_outreach_email(
                candidate_name="Candidate",  # Would be from database in real implementation
                candidate_evidence=evidence_snippets,
                company_name=company_name,
                role_title=role_title,
                recruiter_name=recruiter_name,
                tone=tone
            )
            
            # Parse LLM response
            try:
                email_data = json.loads(llm_result["text"])
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                email_data = {
                    "subject_line": f"Exciting {role_title} opportunity at {company_name}",
                    "email_body": llm_result["text"],
                    "call_to_action": "I'd love to schedule a brief call to discuss this opportunity.",
                    "personalization_points": ["Custom analysis included"],
                    "tone_analysis": {"professional": 0.8}
                }
            
            total_time = time.time() - start_time
            
            result = {
                "candidate_id": candidate_id,
                "email_content": email_data,
                "evidence_used": evidence_snippets,
                "personalization_metadata": {
                    "evidence_chunks_used": len(evidence_snippets),
                    "personalization_score": len(evidence_snippets) / 5.0,  # Simple scoring
                    "generation_time_seconds": total_time,
                    "model_used": llm_result.get("model", "unknown"),
                    "tone_requested": tone
                }
            }
            
            logger.log_email_generation(
                candidate_id=candidate_id,
                email_length=len(email_data.get("email_body", "")),
                personalization_points=len(evidence_snippets),
                generation_time=total_time
            )
            
            return result
            
        except Exception as e:
            total_time = time.time() - start_time
            error_msg = f"Email generation failed: {str(e)}"
            
            logger.log_email_generation(
                candidate_id=candidate_id,
                email_length=0,
                personalization_points=0,
                generation_time=total_time,
                error=error_msg
            )
            
            raise RAGException(error_msg)
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Comprehensive health check for all RAG components.
        
        Returns:
            dict: Health status for all components
        """
        health_status = {
            "rag_engine": "healthy",
            "timestamp": time.time(),
            "components": {}
        }
        
        # Check LLM service
        try:
            llm_healthy = await self.llm_service.health_check()
            health_status["components"]["llm_service"] = {
                "status": "healthy" if llm_healthy else "unhealthy",
                "provider": "openai"
            }
        except Exception as e:
            health_status["components"]["llm_service"] = {
                "status": "unhealthy",
                "error": str(e),
                "provider": "openai"
            }
        
        # Check vector store
        try:
            vector_healthy = await self.vector_store.health_check()
            health_status["components"]["vector_store"] = {
                "status": "healthy" if vector_healthy else "unhealthy",
                "provider": "chromadb"
            }
        except Exception as e:
            health_status["components"]["vector_store"] = {
                "status": "unhealthy",
                "error": str(e),
                "provider": "chromadb"
            }
        
        # Check document processor
        health_status["components"]["document_processor"] = {
            "status": "healthy",
            "supported_formats": list(self.document_processor.allowed_extensions)
        }
        
        # Determine overall health
        component_statuses = [
            comp["status"] for comp in health_status["components"].values()
        ]
        
        if all(status == "healthy" for status in component_statuses):
            health_status["rag_engine"] = "healthy"
        elif any(status == "healthy" for status in component_statuses):
            health_status["rag_engine"] = "degraded"
        else:
            health_status["rag_engine"] = "unhealthy"
        
        return health_status


# Global service instance
_rag_engine: Optional[RAGEngine] = None


def get_rag_engine() -> RAGEngine:
    """
    Get or create RAG engine instance.
    
    Returns:
        RAGEngine: Engine instance
    """
    global _rag_engine
    
    if _rag_engine is None:
        _rag_engine = RAGEngine()
    
    return _rag_engine
