"""
Authentication and authorization endpoints.
Handles user registration, login, token management.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
import structlog

from app.db.database import get_db
from app.schemas.auth import (
    UserCreate, UserLogin, Token, TokenRefresh, 
    UserResponse, LoginResponse, PasswordChange
)
from app.services.auth_service import get_auth_service
from app.models.user import User

router = APIRouter()
security = HTTPBearer()
logger = structlog.get_logger(__name__)


async def get_current_user_dependency(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user."""
    auth_service = get_auth_service()
    return await auth_service.get_current_user(db, credentials.credentials)


@router.post("/register", response_model=UserResponse)
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> UserResponse:
    """
    Register a new user account.
    
    Args:
        user_data: User registration data
        db: Database session
        
    Returns:
        UserResponse: Created user information
    """
    try:
        logger.info("User registration started", email=user_data.email)
        
        auth_service = get_auth_service()
        user = await auth_service.create_user(db, user_data)
        
        logger.info("User registration completed", user_id=str(user.id), email=user.email)
        
        return UserResponse(
            id=str(user.id),
            email=user.email,
            full_name=user.full_name,
            company=user.company,
            phone=user.phone,
            timezone=user.timezone,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            last_login=user.last_login
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User registration failed", email=user_data.email, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=LoginResponse)
async def login_user(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db)
) -> LoginResponse:
    """
    Authenticate user and return access tokens.
    
    Args:
        login_data: User login credentials
        db: Database session
        
    Returns:
        LoginResponse: User info and JWT tokens
    """
    try:
        logger.info("User login attempt", email=login_data.email)
        
        auth_service = get_auth_service()
        token = await auth_service.login_user(db, login_data)
        
        # Get user info for response
        user = await auth_service.get_user_by_email(db, login_data.email)
        
        user_response = UserResponse(
            id=str(user.id),
            email=user.email,
            full_name=user.full_name,
            company=user.company,
            phone=user.phone,
            timezone=user.timezone,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            last_login=user.last_login
        )
        
        logger.info("User login successful", user_id=str(user.id), email=user.email)
        
        return LoginResponse(
            user=user_response,
            token=token,
            message="Login successful"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User login failed", email=login_data.email, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: TokenRefresh,
    db: AsyncSession = Depends(get_db)
) -> Token:
    """
    Refresh access token using refresh token.
    
    Args:
        token_data: Refresh token data
        db: Database session
        
    Returns:
        Token: New access token and same refresh token
    """
    try:
        auth_service = get_auth_service()
        new_token = await auth_service.refresh_access_token(db, token_data.refresh_token)
        
        logger.info("Token refreshed successfully")
        return new_token
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout_user(
    current_user: User = Depends(get_current_user_dependency)
) -> Dict[str, str]:
    """
    Logout user (token invalidation handled client-side).
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        dict: Logout confirmation
    """
    logger.info("User logout", user_id=str(current_user.id), email=current_user.email)
    
    return {
        "message": "Logged out successfully",
        "detail": "Token invalidation should be handled client-side"
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user: User = Depends(get_current_user_dependency)
) -> UserResponse:
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        UserResponse: Current user information
    """
    return UserResponse(
        id=str(current_user.id),
        email=current_user.email,
        full_name=current_user.full_name,
        company=current_user.company,
        phone=current_user.phone,
        timezone=current_user.timezone,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        created_at=current_user.created_at,
        last_login=current_user.last_login
    )


@router.put("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_user_dependency),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """
    Change user password.
    
    Args:
        password_data: Current and new password
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        dict: Password change confirmation
    """
    try:
        auth_service = get_auth_service()
        
        # Verify current password
        if not auth_service.verify_password(password_data.current_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # Update password
        current_user.hashed_password = auth_service.get_password_hash(password_data.new_password)
        await db.commit()
        
        logger.info("Password changed successfully", user_id=str(current_user.id))
        
        return {"message": "Password changed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("Password change failed", user_id=str(current_user.id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )
