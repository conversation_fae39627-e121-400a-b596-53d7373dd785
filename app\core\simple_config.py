"""
Minimal configuration for basic API functionality.
"""

import os
from typing import Optional

class SimpleConfig:
    """Simple configuration class for basic functionality."""
    
    def __init__(self):
        # App basics
        self.app_name = os.getenv("APP_NAME", "Recruitment RAG System")
        self.app_version = os.getenv("APP_VERSION", "1.0.0")
        self.debug = os.getenv("DEBUG", "true").lower() == "true"
        self.environment = os.getenv("ENVIRONMENT", "development")
        
        # API settings
        self.api_host = os.getenv("API_HOST", "127.0.0.1")
        self.api_port = int(os.getenv("API_PORT", "8000"))
        self.api_prefix = os.getenv("API_PREFIX", "/api/v1")
        
        # Security
        self.secret_key = os.getenv("SECRET_KEY", "dev-secret-key-not-for-production")
        
        # Database
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///./rag_dev.db")
        
        # File storage
        self.upload_dir = os.getenv("UPLOAD_DIR", "./data/uploads")
        self.chroma_persist_directory = os.getenv("CHROMA_PERSIST_DIRECTORY", "./data/chroma")
        
        # LLM
        self.openai_api_key = os.getenv("OPENAI_API_KEY", "your-openai-api-key-here")
        
        # Logging
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_format = os.getenv("LOG_FORMAT", "console")
        
        # Create directories
        os.makedirs(self.upload_dir, exist_ok=True)
        os.makedirs(self.chroma_persist_directory, exist_ok=True)


def get_simple_settings() -> SimpleConfig:
    """Get simple configuration for basic functionality."""
    return SimpleConfig()
