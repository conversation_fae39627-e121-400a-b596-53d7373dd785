#!/usr/bin/env python3
"""
Simple LM Studio Configuration Test

Tests the LLM configuration without loading the full application.
"""

import os
import sys
from pathlib import Path

# Add project root to path  
sys.path.append(str(Path(__file__).parent))

def test_config():
    """Test the configuration loading."""
    print("LM Studio Integration - Configuration Test")
    print("=" * 50)
    
    # Test environment variables
    print("\nEnvironment Configuration:")
    
    provider = os.getenv('LLM_PROVIDER', 'openai')
    print(f"   Current Provider: {provider}")
    
    # OpenAI settings
    openai_key = os.getenv('LLM_OPENAI_API_KEY', os.getenv('OPENAI_API_KEY', 'not-set'))
    openai_model = os.getenv('LLM_OPENAI_MODEL', 'gpt-4-1106-preview')
    print(f"   OpenAI API Key: {'Set' if openai_key != 'not-set' and openai_key != 'your-openai-api-key-here' else 'Not Set'}")
    print(f"   OpenAI Model: {openai_model}")
    
    # LM Studio settings
    lmstudio_host = os.getenv('LLM_LMSTUDIO_HOST', 'http://localhost:1234')
    lmstudio_model = os.getenv('LLM_LMSTUDIO_MODEL', 'local-model')
    print(f"   LM Studio Host: {lmstudio_host}")
    print(f"   LM Studio Model: {lmstudio_model}")
    
    print(f"\nConfiguration Status:")
    
    if provider == "openai":
        if openai_key not in ['not-set', 'your-openai-api-key-here']:
            print("   Ready for OpenAI")
        else:
            print("   OpenAI API key needed")
            print("   Set LLM_OPENAI_API_KEY in your .env file")
    
    elif provider == "lmstudio":
        print("   Configured for LM Studio")
        print(f"   Will connect to: {lmstudio_host}")
        print(f"   Expected model: {lmstudio_model}")
        print("   Make sure LM Studio server is running!")
    
    else:
        print(f"   Unknown provider: {provider}")


def show_setup_guide():
    """Show setup instructions."""
    print(f"\nSetup Instructions:")
    print("=" * 30)
    
    print("\nTo use OpenAI:")
    print("   1. Get API key from: https://platform.openai.com/api-keys")
    print("   2. Edit .env file:")
    print("      LLM_PROVIDER=openai")
    print("      LLM_OPENAI_API_KEY=your-actual-api-key")
    
    print("\nTo use LM Studio:")
    print("   1. Download LM Studio from: https://lmstudio.ai/")
    print("   2. Install and download a model (e.g., Llama 3.1 8B)")
    print("   3. Start LM Studio server (usually http://localhost:1234)")
    print("   4. Edit .env file:")
    print("      LLM_PROVIDER=lmstudio")
    print("      LLM_LMSTUDIO_HOST=http://localhost:1234")
    print("      LLM_LMSTUDIO_MODEL=your-model-name")
    
    print("\nTo switch providers:")
    print("   • Just change LLM_PROVIDER in .env file")
    print("   • Restart your RAG system")
    print("   • Same code works with both!")


def test_provider_connection():
    """Test connection to the configured provider."""
    print(f"\nTesting Provider Connection:")
    print("=" * 35)
    
    provider = os.getenv('LLM_PROVIDER', 'openai')
    
    if provider == "lmstudio":
        # Test LM Studio connection
        import httpx
        
        lmstudio_host = os.getenv('LLM_LMSTUDIO_HOST', 'http://localhost:1234')
        
        try:
            print(f"Testing connection to {lmstudio_host}...")
            
            with httpx.Client(timeout=5.0) as client:
                response = client.get(f"{lmstudio_host}/v1/models")
                
                if response.status_code == 200:
                    models = response.json()
                    available_models = [model.get("id", "unknown") for model in models.get("data", [])]
                    
                    print(f"LM Studio server is running!")
                    print(f"Available models: {', '.join(available_models) if available_models else 'None loaded'}")
                    
                    configured_model = os.getenv('LLM_LMSTUDIO_MODEL', 'local-model')
                    if configured_model in available_models or not available_models:
                        print(f"Model '{configured_model}' is ready!")
                    else:
                        print(f"Model '{configured_model}' not found in available models")
                        print(f"Update LLM_LMSTUDIO_MODEL in .env file")
                    
                else:
                    print(f"LM Studio server responded with status: {response.status_code}")
        
        except Exception as e:
            print(f"Cannot connect to LM Studio: {str(e)}")
            print("Make sure LM Studio is running and server is started")
    
    elif provider == "openai":
        print("OpenAI provider configured")
        print("Run full RAG system to test OpenAI connection")
    
    else:
        print(f"Unknown provider: {provider}")


if __name__ == "__main__":
    try:
        test_config()
        show_setup_guide()
        test_provider_connection()
        
        print(f"\nConfiguration Test Complete!")
        print("Next steps:")
        print("   • Fix any configuration issues above")
        print("   • Run: python test_lmstudio.py (full test)")
        print("   • Or start your RAG system normally")
        
    except KeyboardInterrupt:
        print(f"\nTest interrupted")
    except Exception as e:
        print(f"\nTest failed: {str(e)}")
        import traceback
        traceback.print_exc()
