#!/usr/bin/env python3
"""
Complete Production Demo for RAG System Phase 2
Demonstrates all new features: authentication, database, monitoring, background tasks.
"""

import asyncio
import sys
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional

# Add project root to path
sys.path.append(str(Path(__file__).parent))

import httpx
import structlog

logger = structlog.get_logger(__name__)


class ProductionDemo:
    """
    Complete demonstration of production RAG system capabilities.
    """
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/v1"
        self.access_token = None
        self.user_info = None
        self.demo_results = {}
        
        # Demo data
        self.demo_user = {
            "email": "<EMAIL>",
            "full_name": "Production Demo User",
            "password": "demo123secure",
            "company": "RAG Production Demo"
        }
        
        self.demo_job = """
        Senior AI Engineer Position
        
        We are seeking a Senior AI Engineer to join our innovative team:
        
        Requirements:
        - 5+ years of Python development experience
        - Strong background in machine learning and AI
        - Experience with FastAPI, Docker, and cloud platforms
        - Knowledge of vector databases and embeddings
        - Experience with LLMs and NLP technologies
        - Strong problem-solving and communication skills
        
        Preferred:
        - PhD in Computer Science or related field
        - Experience with production ML systems
        - Open source contributions
        - Leadership experience
        """
        
        self.demo_resume = """
        Dr. Sarah Chen
        Senior AI Engineer & Tech Lead
        
        Experience:
        • 7 years of Python development with focus on AI/ML systems
        • Led development of production ML pipelines serving 10M+ users
        • Expert in FastAPI, Docker, Kubernetes, and AWS/GCP
        • Built and deployed vector search systems using ChromaDB and Pinecone
        • Extensive experience with OpenAI APIs, LangChain, and transformer models
        • Published 15+ research papers on NLP and machine learning
        
        Education:
        • PhD Computer Science, Stanford University (AI/ML focus)
        • MS Computer Science, MIT
        • BS Computer Science, UC Berkeley
        
        Technical Skills:
        • Languages: Python, Go, JavaScript, SQL
        • ML/AI: PyTorch, TensorFlow, scikit-learn, Hugging Face
        • Platforms: AWS, GCP, Docker, Kubernetes, FastAPI
        • Databases: PostgreSQL, MongoDB, ChromaDB, Pinecone
        • Tools: Git, CI/CD, Prometheus, Grafana
        
        Leadership:
        • Led AI team of 12 engineers at TechCorp (2021-2024)
        • Open source maintainer with 50K+ GitHub stars
        • Conference speaker at NeurIPS, ICML, and PyData
        
        Recent Projects:
        • Built RAG system for legal document analysis (2024)
        • Developed multi-modal AI assistant with 99.2% accuracy (2023)
        • Architected real-time recommendation engine (2022)
        """
    
    async def run_complete_demo(self) -> bool:
        """Run complete production demo."""
        print("🚀 RAG Production System - Complete Demo")
        print("=" * 60)
        print("Demonstrating: Authentication, RAG Pipeline, Monitoring, Background Tasks")
        print("=" * 60)
        
        try:
            # Check system health
            await self.check_system_health()
            
            # Demo authentication
            await self.demo_authentication()
            
            # Demo RAG pipeline
            await self.demo_rag_pipeline()
            
            # Demo monitoring
            await self.demo_monitoring()
            
            # Demo background tasks
            await self.demo_background_tasks()
            
            # Generate final report
            return self.generate_demo_report()
            
        except KeyboardInterrupt:
            print("\n⏹️  Demo interrupted by user")
            return False
        except Exception as e:
            logger.error("Demo failed", error=str(e), exc_info=True)
            print(f"\n💥 Demo failed: {e}")
            return False
    
    async def check_system_health(self):
        """Check system health before starting demo."""
        print("\n🏥 Step 1: System Health Check...")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200:
                    health_data = response.json()
                    print(f"   ✅ System Status: {health_data['status']}")
                    
                    # Show component status
                    for component, status in health_data.get('components', {}).items():
                        if isinstance(status, dict):
                            comp_status = status.get('status', 'unknown')
                            if 'status' in status:
                                emoji = "✅" if comp_status == 'healthy' else "⚠️" if comp_status == 'degraded' else "❌"
                                print(f"   {emoji} {component}: {comp_status}")
                            else:
                                # Handle nested components (like rag_engine)
                                for sub_comp, sub_status in status.items():
                                    if isinstance(sub_status, dict) and 'status' in sub_status:
                                        sub_stat = sub_status['status']
                                        emoji = "✅" if sub_stat == 'healthy' else "⚠️" if sub_stat == 'degraded' else "❌"
                                        print(f"   {emoji} {component}.{sub_comp}: {sub_stat}")
                    
                    self.demo_results['health_check'] = True
                else:
                    print(f"   ❌ Health check failed: {response.status_code}")
                    self.demo_results['health_check'] = False
                    
        except Exception as e:
            print(f"   ❌ Health check error: {e}")
            self.demo_results['health_check'] = False
    
    async def demo_authentication(self):
        """Demo authentication system."""
        print("\n🔐 Step 2: Authentication Demo...")
        
        try:
            async with httpx.AsyncClient() as client:
                # Step 1: Register user
                print("   📝 Registering demo user...")
                response = await client.post(
                    f"{self.api_url}/auth/register",
                    json=self.demo_user
                )
                
                if response.status_code in [200, 201]:
                    print("   ✅ User registration successful")
                elif response.status_code == 400 and "already exists" in response.text:
                    print("   ✅ User already exists (continuing)")
                else:
                    print(f"   ❌ Registration failed: {response.status_code}")
                    self.demo_results['authentication'] = False
                    return
                
                # Step 2: Login
                print("   🔑 Logging in...")
                login_data = {
                    "email": self.demo_user["email"],
                    "password": self.demo_user["password"]
                }
                
                response = await client.post(
                    f"{self.api_url}/auth/login",
                    json=login_data
                )
                
                if response.status_code == 200:
                    auth_data = response.json()
                    self.access_token = auth_data["token"]["access_token"]
                    self.user_info = auth_data["user"]
                    
                    print("   ✅ Login successful")
                    print(f"   👤 User: {self.user_info['full_name']} ({self.user_info['email']})")
                    print(f"   🏢 Company: {self.user_info.get('company', 'N/A')}")
                    
                    # Step 3: Test protected endpoint
                    print("   🔒 Testing protected endpoint...")
                    headers = {"Authorization": f"Bearer {self.access_token}"}
                    response = await client.get(f"{self.api_url}/auth/me", headers=headers)
                    
                    if response.status_code == 200:
                        print("   ✅ Protected endpoint access successful")
                        self.demo_results['authentication'] = True
                    else:
                        print(f"   ❌ Protected endpoint failed: {response.status_code}")
                        self.demo_results['authentication'] = False
                else:
                    print(f"   ❌ Login failed: {response.status_code}")
                    self.demo_results['authentication'] = False
                    
        except Exception as e:
            print(f"   ❌ Authentication demo error: {e}")
            self.demo_results['authentication'] = False
    
    async def demo_rag_pipeline(self):
        """Demo complete RAG pipeline with authentication."""
        print("\n🤖 Step 3: RAG Pipeline Demo...")
        
        if not self.access_token:
            print("   ❌ No access token - skipping RAG demo")
            self.demo_results['rag_pipeline'] = False
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                # Step 1: Upload resume
                print("   📄 Uploading candidate resume...")
                
                # Create temporary file for upload
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                    f.write(self.demo_resume)
                    temp_file_path = f.name
                
                with open(temp_file_path, 'rb') as f:
                    files = {'file': ('sarah_chen_resume.txt', f, 'text/plain')}
                    data = {'candidate_name': 'Dr. Sarah Chen', 'document_type': 'resume'}
                    
                    response = await client.post(
                        f"{self.api_url}/documents/upload",
                        headers=headers,
                        files=files,
                        data=data
                    )
                
                # Clean up temp file
                import os
                os.unlink(temp_file_path)
                
                if response.status_code == 200:
                    upload_data = response.json()
                    candidate_id = upload_data["candidate_id"]
                    print(f"   ✅ Resume uploaded successfully")
                    print(f"   👤 Candidate ID: {candidate_id}")
                    print(f"   📊 Chunks created: {upload_data['processing_results']['total_chunks_created']}")
                    
                    # Step 2: Score candidate
                    print("   🎯 Scoring candidate against job...")
                    
                    scoring_request = {
                        "candidate_id": candidate_id,
                        "job_description": self.demo_job,
                        "ideal_candidate_profile": "Senior AI engineer with PhD and production ML experience"
                    }
                    
                    response = await client.post(
                        f"{self.api_url}/scoring/score",
                        headers=headers,
                        json=scoring_request
                    )
                    
                    if response.status_code == 200:
                        scoring_data = response.json()
                        score = scoring_data["scoring_results"]["overall_score"]
                        percentage = scoring_data["scoring_results"]["overall_percentage"]
                        fit_level = scoring_data["scoring_results"]["fit_level"]
                        
                        print(f"   ✅ Scoring completed successfully")
                        print(f"   📊 Overall Score: {score:.2f} ({percentage}%)")
                        print(f"   🎯 Fit Level: {fit_level}")
                        print(f"   📝 Evidence Chunks: {scoring_data['evidence']['evidence_chunks_analyzed']}")
                        
                        # Show strengths and gaps
                        strengths = scoring_data["scoring_results"].get("notable_strengths", [])
                        if strengths:
                            print(f"   💪 Strengths: {', '.join(strengths[:2])}")
                        
                        # Step 3: Generate outreach email
                        print("   📧 Generating personalized outreach email...")
                        
                        email_request = {
                            "candidate_id": candidate_id,
                            "company_name": "InnovateTech AI",
                            "role_title": "Senior AI Engineer",
                            "recruiter_name": "Jessica Miller",
                            "tone": "professional"
                        }
                        
                        response = await client.post(
                            f"{self.api_url}/outreach/generate-email",
                            headers=headers,
                            json=email_request
                        )
                        
                        if response.status_code == 200:
                            email_data = response.json()
                            email_content = email_data["email_content"]
                            
                            print(f"   ✅ Email generated successfully")
                            print(f"   📧 Subject: {email_content['subject_line']}")
                            print(f"   📄 Email length: {len(email_content['email_body'])} characters")
                            print(f"   🎯 Personalization points: {email_data['personalization_details']['evidence_chunks_used']}")
                            
                            # Show first few lines of email
                            email_preview = email_content['email_body'][:200] + "..."
                            print(f"   📖 Preview: {email_preview}")
                            
                            self.demo_results['rag_pipeline'] = True
                        else:
                            print(f"   ❌ Email generation failed: {response.status_code}")
                            self.demo_results['rag_pipeline'] = False
                    else:
                        print(f"   ❌ Scoring failed: {response.status_code}")
                        self.demo_results['rag_pipeline'] = False
                else:
                    print(f"   ❌ Resume upload failed: {response.status_code}")
                    print(f"   Response: {response.text}")
                    self.demo_results['rag_pipeline'] = False
                    
        except Exception as e:
            print(f"   ❌ RAG pipeline demo error: {e}")
            self.demo_results['rag_pipeline'] = False
    
    async def demo_monitoring(self):
        """Demo monitoring and metrics."""
        print("\n📊 Step 4: Monitoring Demo...")
        
        try:
            async with httpx.AsyncClient() as client:
                # Check metrics endpoint
                response = await client.get(f"{self.base_url}/metrics")
                
                if response.status_code == 200:
                    metrics_data = response.text
                    
                    # Count metrics
                    lines = metrics_data.split('\n')
                    metric_lines = [line for line in lines if line and not line.startswith('#')]
                    
                    print(f"   ✅ Metrics endpoint accessible")
                    print(f"   📈 Active metrics: {len(metric_lines)}")
                    
                    # Show some key metrics
                    key_metrics = [
                        'rag_http_requests_total',
                        'rag_documents_processed_total', 
                        'rag_candidate_scores_generated_total'
                    ]
                    
                    for metric in key_metrics:
                        matching_lines = [line for line in metric_lines if metric in line]
                        if matching_lines:
                            print(f"   📊 {metric}: Found {len(matching_lines)} data points")
                    
                    self.demo_results['monitoring'] = True
                else:
                    print(f"   ❌ Metrics endpoint failed: {response.status_code}")
                    self.demo_results['monitoring'] = False
                    
        except Exception as e:
            print(f"   ❌ Monitoring demo error: {e}")
            self.demo_results['monitoring'] = False
    
    async def demo_background_tasks(self):
        """Demo background task capabilities."""
        print("\n⚙️  Step 5: Background Tasks Demo...")
        
        try:
            # Check if Celery/Redis is configured
            from app.core.config import get_settings
            settings = get_settings()
            
            if settings.redis.url:
                print("   ✅ Redis configuration found")
                
                # Test Redis connection
                try:
                    import redis
                    redis_client = redis.from_url(settings.redis.url)
                    redis_client.ping()
                    print("   ✅ Redis connection successful")
                    
                    # Check Celery configuration
                    from app.workers.celery_app import celery_app
                    if celery_app.conf.broker_url:
                        print("   ✅ Celery configuration loaded")
                        print("   💡 Background task system ready")
                        
                        # Show available tasks
                        tasks = list(celery_app.tasks.keys())
                        app_tasks = [task for task in tasks if task.startswith('app.')]
                        if app_tasks:
                            print(f"   📋 Available tasks: {len(app_tasks)}")
                            for task in app_tasks[:3]:  # Show first 3
                                print(f"      - {task}")
                        
                        self.demo_results['background_tasks'] = True
                    else:
                        print("   ⚠️  Celery configuration incomplete")
                        self.demo_results['background_tasks'] = False
                        
                except ImportError:
                    print("   ⚠️  Redis package not available")
                    self.demo_results['background_tasks'] = False
                except Exception as e:
                    print(f"   ⚠️  Redis connection failed: {e}")
                    print("   💡 Start Redis server for full background task support")
                    self.demo_results['background_tasks'] = False
            else:
                print("   ⚠️  Redis not configured")
                self.demo_results['background_tasks'] = False
                
        except Exception as e:
            print(f"   ❌ Background tasks demo error: {e}")
            self.demo_results['background_tasks'] = False
    
    def generate_demo_report(self) -> bool:
        """Generate final demo report."""
        print("\n" + "=" * 60)
        print("📋 PRODUCTION DEMO REPORT")
        print("=" * 60)
        
        demo_categories = {
            'health_check': 'System Health Check',
            'authentication': 'Authentication System',
            'rag_pipeline': 'RAG Pipeline (Core Feature)',
            'monitoring': 'Monitoring & Metrics',
            'background_tasks': 'Background Task System'
        }
        
        success_count = 0
        total_count = len(demo_categories)
        
        for key, name in demo_categories.items():
            success = self.demo_results.get(key, False)
            status = "✅" if success else "❌"
            print(f"{status} {name}")
            if success:
                success_count += 1
        
        print(f"\n📊 Overall: {success_count}/{total_count} demos completed successfully")
        
        if success_count == total_count:
            print("\n🎉 ALL PRODUCTION FEATURES WORKING!")
            print("\n🚀 Your production RAG system is fully operational:")
            print("   • Secure multi-user authentication")
            print("   • Complete RAG pipeline with evidence-based scoring")
            print("   • Real-time monitoring and metrics")
            print("   • Background processing capabilities")
            print("   • Production-ready architecture")
            print("\n💼 Ready for enterprise deployment!")
            return True
            
        elif success_count >= 3:
            print("\n✅ CORE FEATURES WORKING!")
            print("Your RAG system is operational with minor issues.")
            print("Core authentication and RAG pipeline are functional.")
            return True
        else:
            print("\n⚠️  ISSUES DETECTED")
            print("Please check the failed components above.")
            print("Some core features may not be working correctly.")
            return False


async def main():
    """Main demo function."""
    print("🎯 Starting Production RAG System Demo...")
    print("This will test all Phase 2 features with real API calls.")
    print("Ensure your system is running: python production_server.py")
    print()
    
    # Check if server is running
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/health", timeout=5.0)
            if response.status_code != 200:
                print("❌ Server not responding properly")
                print("Please start the server: python production_server.py")
                return False
    except Exception:
        print("❌ Cannot connect to server at http://localhost:8000")
        print("Please start the server: python production_server.py")
        return False
    
    demo = ProductionDemo()
    success = await demo.run_complete_demo()
    
    if success:
        print("\n🎊 Demo completed successfully!")
        print("Your production RAG system is ready for real-world use!")
    else:
        print("\n💡 For troubleshooting:")
        print("   - Check server logs")
        print("   - Verify OpenAI API key is set")
        print("   - Ensure all services are running")
        print("   - Run: python production_setup.py")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
