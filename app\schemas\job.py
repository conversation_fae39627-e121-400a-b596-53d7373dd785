"""
Job posting schemas for request/response validation.
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime


class JobCreate(BaseModel):
    """Schema for creating a new job posting."""
    title: str
    company: str
    department: Optional[str] = None
    location: str
    remote_allowed: bool = False
    job_type: str = "full-time"  # full-time, part-time, contract
    experience_level: str = "mid"  # entry, mid, senior, executive
    description: str
    requirements: List[str]
    nice_to_have: Optional[List[str]] = None
    responsibilities: Optional[List[str]] = None
    benefits: Optional[List[str]] = None
    salary_range_min: Optional[int] = None
    salary_range_max: Optional[int] = None
    ideal_candidate_profile: Optional[str] = None
    company_culture: Optional[str] = None


class JobUpdate(BaseModel):
    """Schema for updating job posting."""
    title: Optional[str] = None
    company: Optional[str] = None
    department: Optional[str] = None
    location: Optional[str] = None
    remote_allowed: Optional[bool] = None
    job_type: Optional[str] = None
    experience_level: Optional[str] = None
    description: Optional[str] = None
    requirements: Optional[List[str]] = None
    nice_to_have: Optional[List[str]] = None
    responsibilities: Optional[List[str]] = None
    benefits: Optional[List[str]] = None
    salary_range_min: Optional[int] = None
    salary_range_max: Optional[int] = None
    ideal_candidate_profile: Optional[str] = None
    company_culture: Optional[str] = None
    is_active: Optional[bool] = None


class JobResponse(BaseModel):
    """Schema for job posting response."""
    id: UUID
    title: str
    company: str
    department: Optional[str] = None
    location: str
    remote_allowed: bool
    job_type: str
    experience_level: str
    description: str
    requirements: List[str]
    nice_to_have: Optional[List[str]] = None
    responsibilities: Optional[List[str]] = None
    benefits: Optional[List[str]] = None
    salary_range_min: Optional[int] = None
    salary_range_max: Optional[int] = None
    ideal_candidate_profile: Optional[str] = None
    company_culture: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class JobRequirementsAnalysis(BaseModel):
    """Schema for structured job requirements analysis."""
    required_skills: List[str]
    required_experience_years: Optional[int] = None
    required_education: Optional[str] = None
    nice_to_have_skills: List[str]
    key_responsibilities: List[str]
    culture_fit_indicators: List[str]
    deal_breakers: Optional[List[str]] = None
    analysis_confidence: float  # 0.0 to 1.0
