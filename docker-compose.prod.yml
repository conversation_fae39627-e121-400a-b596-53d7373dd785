# =============================================================================
# Docker Compose for RAG System Production
# Optimized for production deployment with scaling and security
# =============================================================================

version: '3.8'

services:
  # -----------------------------------------------------------------------------
  # PostgreSQL Database (Production)
  # -----------------------------------------------------------------------------
  postgres:
    image: postgres:15-alpine
    container_name: rag-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # -----------------------------------------------------------------------------
  # Redis (Production)
  # -----------------------------------------------------------------------------
  redis:
    image: redis:7-alpine
    container_name: rag-redis-prod
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.3'

  # -----------------------------------------------------------------------------
  # ChromaDB (Production)
  # -----------------------------------------------------------------------------
  chroma:
    image: ghcr.io/chroma-core/chroma:latest
    container_name: rag-chroma-prod
    environment:
      CHROMA_SERVER_HOST: 0.0.0.0
      CHROMA_SERVER_HTTP_PORT: 8001
    volumes:
      - chroma_data:/chroma/chroma
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # -----------------------------------------------------------------------------
  # Main API Application (Production)
  # -----------------------------------------------------------------------------
  api:
    build:
      context: .
      target: production
    container_name: rag-api-prod
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      
      # ChromaDB
      CHROMA_HOST: chroma
      CHROMA_PORT: 8001
      
      # OpenAI
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      OPENAI_MODEL: ${OPENAI_MODEL:-gpt-4-1106-preview}
      OPENAI_EMBEDDING_MODEL: ${OPENAI_EMBEDDING_MODEL:-text-embedding-3-large}
      
      # App settings
      DEBUG: false
      ENVIRONMENT: production
      SECRET_KEY: ${SECRET_KEY}
      
      # Logging
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_FORMAT: json
      
      # Security
      ALLOWED_HOSTS: ${ALLOWED_HOSTS}
    ports:
      - "8000:8000"
    volumes:
      - api_data:/app/data
      - api_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      chroma:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # -----------------------------------------------------------------------------
  # Celery Worker (Production)
  # -----------------------------------------------------------------------------
  worker:
    build:
      context: .
      target: production
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
      
      # ChromaDB
      CHROMA_HOST: chroma
      CHROMA_PORT: 8001
      
      # OpenAI
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      
      # App settings
      ENVIRONMENT: production
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
    volumes:
      - api_data:/app/data
      - api_logs:/app/logs
    depends_on:
      - postgres
      - redis
      - chroma
    command: celery -A app.workers.celery_app worker --loglevel=info --concurrency=4
    restart: always
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '1.0'

  # -----------------------------------------------------------------------------
  # Nginx (Reverse Proxy & Load Balancer)
  # -----------------------------------------------------------------------------
  nginx:
    image: nginx:alpine
    container_name: rag-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - api
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.3'

  # -----------------------------------------------------------------------------
  # Prometheus (Production Monitoring)
  # -----------------------------------------------------------------------------
  prometheus:
    image: prom/prometheus:latest
    container_name: rag-prometheus-prod
    volumes:
      - ./config/prometheus-prod.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # -----------------------------------------------------------------------------
  # Grafana (Production Dashboards)
  # -----------------------------------------------------------------------------
  grafana:
    image: grafana/grafana:latest
    container_name: rag-grafana-prod
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_SECURITY_SECRET_KEY: ${GRAFANA_SECRET_KEY}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana-prod-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
      - ./config/grafana-dashboards:/etc/grafana/provisioning/dashboards
    depends_on:
      - prometheus
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

# =============================================================================
# Volumes (Production)
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  chroma_data:
    driver: local
  api_data:
    driver: local
  api_logs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# =============================================================================
# Networks
# =============================================================================
networks:
  default:
    name: rag-prod-network
    driver: bridge
