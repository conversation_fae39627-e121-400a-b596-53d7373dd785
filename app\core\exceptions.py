"""
Custom exceptions for the RAG system.
Provides structured error handling with proper HTTP status codes.
"""

from typing import Optional, Dict, Any


class RAGException(Exception):
    """
    Base exception class for RAG system errors.
    All custom exceptions should inherit from this class.
    """
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_type: str = "RAG_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_type = error_type
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(RAGException):
    """Raised when input validation fails."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=400,
            error_type="VALIDATION_ERROR",
            details=details
        )


class DocumentProcessingError(RAGException):
    """Raised when document processing fails."""
    
    def __init__(self, message: str, filename: Optional[str] = None):
        details = {"filename": filename} if filename else {}
        super().__init__(
            message=message,
            status_code=422,
            error_type="DOCUMENT_PROCESSING_ERROR",
            details=details
        )


class EmbeddingError(RAGException):
    """Raised when embedding generation fails."""
    
    def __init__(self, message: str, provider: Optional[str] = None):
        details = {"provider": provider} if provider else {}
        super().__init__(
            message=message,
            status_code=503,
            error_type="EMBEDDING_ERROR",
            details=details
        )


class VectorStoreError(RAGException):
    """Raised when vector store operations fail."""
    
    def __init__(self, message: str, operation: Optional[str] = None):
        details = {"operation": operation} if operation else {}
        super().__init__(
            message=message,
            status_code=503,
            error_type="VECTOR_STORE_ERROR",
            details=details
        )


class LLMError(RAGException):
    """Raised when LLM operations fail."""
    
    def __init__(self, message: str, model: Optional[str] = None, tokens_used: Optional[int] = None):
        details = {}
        if model:
            details["model"] = model
        if tokens_used:
            details["tokens_used"] = tokens_used
            
        super().__init__(
            message=message,
            status_code=503,
            error_type="LLM_ERROR",
            details=details
        )


class AuthenticationError(RAGException):
    """Raised when authentication fails."""
    
    def __init__(self, message: str = "Authentication required"):
        super().__init__(
            message=message,
            status_code=401,
            error_type="AUTHENTICATION_ERROR"
        )


class AuthorizationError(RAGException):
    """Raised when authorization fails."""
    
    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(
            message=message,
            status_code=403,
            error_type="AUTHORIZATION_ERROR"
        )


class ResourceNotFoundError(RAGException):
    """Raised when a requested resource is not found."""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, resource_id: Optional[str] = None):
        details = {}
        if resource_type:
            details["resource_type"] = resource_type
        if resource_id:
            details["resource_id"] = resource_id
            
        super().__init__(
            message=message,
            status_code=404,
            error_type="RESOURCE_NOT_FOUND",
            details=details
        )


class RateLimitError(RAGException):
    """Raised when rate limits are exceeded."""
    
    def __init__(self, message: str, retry_after: Optional[int] = None):
        details = {"retry_after": retry_after} if retry_after else {}
        super().__init__(
            message=message,
            status_code=429,
            error_type="RATE_LIMIT_ERROR",
            details=details
        )


class ConfigurationError(RAGException):
    """Raised when configuration is invalid."""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        details = {"config_key": config_key} if config_key else {}
        super().__init__(
            message=message,
            status_code=500,
            error_type="CONFIGURATION_ERROR",
            details=details
        )


class ExternalServiceError(RAGException):
    """Raised when external service calls fail."""
    
    def __init__(self, message: str, service: Optional[str] = None, status_code: int = 503):
        details = {"service": service} if service else {}
        super().__init__(
            message=message,
            status_code=status_code,
            error_type="EXTERNAL_SERVICE_ERROR",
            details=details
        )
