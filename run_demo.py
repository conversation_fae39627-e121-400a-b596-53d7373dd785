#!/usr/bin/env python3
"""
One-command demo launcher - installs dependencies and starts the server.
"""

import subprocess
import sys
import os

def run_cmd(cmd):
    """Run command and return success status."""
    try:
        subprocess.run(cmd, shell=True, check=True)
        return True
    except:
        return False

print("🚀 RAG System Demo Launcher")
print("=" * 40)

# Install minimal dependencies
print("📦 Installing FastAPI and uvicorn...")
if run_cmd("pip install fastapi uvicorn"):
    print("✅ Dependencies installed!")
else:
    print("❌ Installation failed. Trying to continue anyway...")

print("")
print("🎉 Starting RAG System Demo...")
print("")
print("📍 Access Points:")
print("🌐 API Documentation: http://localhost:8000/api/v1/docs")
print("🔍 Health Check: http://localhost:8000/health")
print("📊 Alternative Docs: http://localhost:8000/api/v1/redoc")
print("")
print("💡 This shows the complete RAG system design!")
print("💡 No complex setup - just explore the API docs!")
print("")
print("Press Ctrl+C to stop")
print("=" * 50)

# Start the demo server
try:
    exec(open('demo_server.py').read())
except KeyboardInterrupt:
    print("\n✅ Demo stopped. Thanks for exploring!")
except Exception as e:
    print(f"\n❌ Error: {e}")
    print("💡 Try: python demo_server.py")
