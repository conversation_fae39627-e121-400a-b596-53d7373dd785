"""
Candidate schemas for request/response validation.
"""

from pydantic import BaseModel, EmailStr
from typing import Optional, List
from uuid import UUID
from datetime import datetime


class CandidateCreate(BaseModel):
    """Schema for creating a new candidate."""
    email: EmailStr
    full_name: str
    phone: Optional[str] = None
    linkedin_url: Optional[str] = None
    github_url: Optional[str] = None
    portfolio_url: Optional[str] = None
    current_location: Optional[str] = None
    desired_location: Optional[str] = None
    years_experience: Optional[int] = None
    current_role: Optional[str] = None
    skills: Optional[List[str]] = None
    notes: Optional[str] = None


class CandidateUpdate(BaseModel):
    """Schema for updating candidate information."""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    linkedin_url: Optional[str] = None
    github_url: Optional[str] = None
    portfolio_url: Optional[str] = None
    current_location: Optional[str] = None
    desired_location: Optional[str] = None
    years_experience: Optional[int] = None
    current_role: Optional[str] = None
    skills: Optional[List[str]] = None
    notes: Optional[str] = None


class CandidateResponse(BaseModel):
    """Schema for candidate response."""
    id: UUID
    email: str
    full_name: str
    phone: Optional[str] = None
    linkedin_url: Optional[str] = None
    github_url: Optional[str] = None
    portfolio_url: Optional[str] = None
    current_location: Optional[str] = None
    desired_location: Optional[str] = None
    years_experience: Optional[int] = None
    current_role: Optional[str] = None
    skills: Optional[List[str]] = None
    notes: Optional[str] = None
    has_resume: bool = False
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ResumeUpload(BaseModel):
    """Schema for resume upload response."""
    candidate_id: UUID
    filename: str
    file_size: int
    processing_status: str
    upload_timestamp: datetime
