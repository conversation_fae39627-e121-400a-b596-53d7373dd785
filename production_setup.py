#!/usr/bin/env python3
"""
Production Setup Script for RAG System Phase 2
Handles complete setup: database, authentication, monitoring, and testing.
"""

import asyncio
import sys
import os
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Optional
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent))

import structlog


class ProductionSetup:
    """
    Complete production setup manager for RAG System.
    """
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.logger = structlog.get_logger(__name__)
        self.setup_results = {
            "dependencies": False,
            "environment": False,
            "database": False,
            "migrations": False,
            "initial_data": False,
            "authentication": False,
            "monitoring": False,
            "background_tasks": False,
            "tests": False
        }
        
    async def run_complete_setup(self) -> bool:
        """Run complete production setup."""
        print("🚀 RAG System Production Setup - Phase 2")
        print("=" * 60)
        print("Setting up: Authentication, Database, Monitoring, Background Tasks")
        print("=" * 60)
        
        try:
            # Step 1: Check and install dependencies
            await self.setup_dependencies()
            
            # Step 2: Validate environment configuration
            await self.setup_environment()
            
            # Step 3: Initialize database
            await self.setup_database()
            
            # Step 4: Run database migrations
            await self.setup_migrations()
            
            # Step 5: Create initial data
            await self.setup_initial_data()
            
            # Step 6: Test authentication system
            await self.setup_authentication()
            
            # Step 7: Initialize monitoring
            await self.setup_monitoring()
            
            # Step 8: Test background tasks
            await self.setup_background_tasks()
            
            # Step 9: Run comprehensive tests
            await self.run_tests()
            
            # Final report
            return await self.generate_setup_report()
            
        except KeyboardInterrupt:
            print("\n⏹️  Setup interrupted by user")
            return False
        except Exception as e:
            self.logger.error("Setup failed", error=str(e), exc_info=True)
            print(f"\n💥 Setup failed: {e}")
            return False
    
    async def setup_dependencies(self) -> bool:
        """Install and verify all dependencies."""
        print("\n📦 Step 1: Installing Dependencies...")
        
        try:
            # Check if we're in a virtual environment
            in_venv = hasattr(sys, 'real_prefix') or (
                hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
            )
            
            if not in_venv:
                print("   ⚠️  Not in virtual environment!")
                print("   💡 Recommended: Create virtual environment first")
                print("      python -m venv venv")
                print("      venv\\Scripts\\activate  # Windows")
                print("      source venv/bin/activate  # Linux/Mac")
                
                response = input("   Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    return False
            
            # Install requirements
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("   ✅ Dependencies installed successfully")
                
                # Verify key imports
                try:
                    import fastapi
                    import sqlalchemy
                    import celery
                    import prometheus_client
                    import passlib
                    print("   ✅ Key packages verified")
                    
                    self.setup_results["dependencies"] = True
                    return True
                    
                except ImportError as e:
                    print(f"   ❌ Import verification failed: {e}")
                    return False
            else:
                print(f"   ❌ Dependency installation failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ Dependency setup failed: {e}")
            return False
    
    async def setup_environment(self) -> bool:
        """Validate and setup environment configuration."""
        print("\n🔧 Step 2: Environment Configuration...")
        
        try:
            # Check .env file exists
            env_file = self.project_root / ".env"
            if not env_file.exists():
                print("   ❌ .env file not found!")
                
                # Copy from example
                env_example = self.project_root / ".env.example"
                if env_example.exists():
                    import shutil
                    shutil.copy(env_example, env_file)
                    print("   📝 Created .env from .env.example")
                else:
                    print("   ❌ No .env.example found either!")
                    return False
            
            # Load and validate configuration
            from app.core.config import get_settings
            settings = get_settings()
            
            # Check critical settings
            issues = []
            
            # OpenAI API Key
            if not settings.llm.openai_api_key or settings.llm.openai_api_key == "your-openai-api-key-here":
                issues.append("OPENAI_API_KEY not configured")
            
            # Secret Key
            if settings.security.secret_key == "your-super-secret-key-here-change-in-production":
                issues.append("SECRET_KEY still using default value")
            
            # Database URL
            if "localhost" in settings.database.url and "rag_user:rag_password" in settings.database.url:
                print("   ⚠️  Using default database credentials")
            
            if issues:
                print("   ⚠️  Configuration issues found:")
                for issue in issues:
                    print(f"      - {issue}")
                print("   💡 Please update your .env file with proper values")
                
                response = input("   Continue with current configuration? (y/N): ")
                if response.lower() != 'y':
                    return False
            
            print("   ✅ Environment configuration validated")
            self.setup_results["environment"] = True
            return True
            
        except Exception as e:
            print(f"   ❌ Environment setup failed: {e}")
            return False
    
    async def setup_database(self) -> bool:
        """Initialize database and test connection."""
        print("\n🗄️  Step 3: Database Initialization...")
        
        try:
            # Run database initialization script
            result = subprocess.run([
                sys.executable, "scripts/init_database.py"
            ], capture_output=False, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                print("   ✅ Database initialization completed")
                self.setup_results["database"] = True
                return True
            else:
                print("   ❌ Database initialization failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Database setup failed: {e}")
            return False
    
    async def setup_migrations(self) -> bool:
        """Run database migrations."""
        print("\n🔄 Step 4: Database Migrations...")
        
        try:
            # Run Alembic migrations
            result = subprocess.run([
                "alembic", "upgrade", "head"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                print("   ✅ Database migrations completed")
                self.setup_results["migrations"] = True
                return True
            else:
                print(f"   ❌ Migration failed: {result.stderr}")
                # Try to create initial migration
                print("   🔄 Attempting to create initial migration...")
                
                result = subprocess.run([
                    "alembic", "revision", "--autogenerate", "-m", "Initial migration"
                ], capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    print("   ✅ Initial migration created")
                    # Try upgrade again
                    result = subprocess.run([
                        "alembic", "upgrade", "head"
                    ], capture_output=True, text=True, cwd=self.project_root)
                    
                    if result.returncode == 0:
                        print("   ✅ Migration upgrade successful")
                        self.setup_results["migrations"] = True
                        return True
                
                return False
                
        except Exception as e:
            print(f"   ❌ Migration setup failed: {e}")
            return False
    
    async def setup_initial_data(self) -> bool:
        """Create initial data and test user."""
        print("\n👤 Step 5: Initial Data Setup...")
        
        try:
            # Import after ensuring dependencies are installed
            from app.db.database import init_db, get_db_manager
            from app.services.auth_service import get_auth_service
            from app.schemas.auth import UserCreate
            
            # Initialize database connection
            await init_db()
            
            # Create test admin user
            auth_service = get_auth_service()
            db_manager = get_db_manager()
            
            async with db_manager.async_session_maker() as session:
                # Check if admin user exists
                admin_user = await auth_service.get_user_by_email(session, "<EMAIL>")
                
                if not admin_user:
                    # Create admin user
                    admin_data = UserCreate(
                        email="<EMAIL>",
                        full_name="RAG System Administrator",
                        password="admin123",
                        company="RAG Recruiter",
                        timezone="UTC"
                    )
                    
                    admin_user = await auth_service.create_user(session, admin_data)
                    print("   ✅ Admin user created: <EMAIL> / admin123")
                else:
                    print("   ✅ Admin user already exists")
                
                # Create test regular user
                test_user = await auth_service.get_user_by_email(session, "<EMAIL>")
                
                if not test_user:
                    test_data = UserCreate(
                        email="<EMAIL>",
                        full_name="Test User",
                        password="test123",
                        company="Test Company",
                        timezone="UTC"
                    )
                    
                    test_user = await auth_service.create_user(session, test_data)
                    print("   ✅ Test user created: <EMAIL> / test123")
                else:
                    print("   ✅ Test user already exists")
            
            self.setup_results["initial_data"] = True
            return True
            
        except Exception as e:
            print(f"   ❌ Initial data setup failed: {e}")
            return False
    
    async def setup_authentication(self) -> bool:
        """Test authentication system."""
        print("\n🔐 Step 6: Authentication System Test...")
        
        try:
            from app.services.auth_service import get_auth_service
            from app.schemas.auth import UserLogin
            from app.db.database import get_db_manager
            
            auth_service = get_auth_service()
            db_manager = get_db_manager()
            
            async with db_manager.async_session_maker() as session:
                # Test login
                login_data = UserLogin(email="<EMAIL>", password="admin123")
                token = await auth_service.login_user(session, login_data)
                
                if token.access_token:
                    print("   ✅ Authentication login test passed")
                    
                    # Test token verification
                    payload = auth_service.verify_token(token.access_token)
                    if payload:
                        print("   ✅ Token verification test passed")
                        
                        # Test get current user
                        user = await auth_service.get_current_user(session, token.access_token)
                        if user:
                            print("   ✅ Get current user test passed")
                            self.setup_results["authentication"] = True
                            return True
            
            return False
            
        except Exception as e:
            print(f"   ❌ Authentication test failed: {e}")
            return False
    
    async def setup_monitoring(self) -> bool:
        """Initialize monitoring and metrics."""
        print("\n📊 Step 7: Monitoring System...")
        
        try:
            from app.services.metrics import get_metrics
            
            # Initialize metrics
            metrics = get_metrics()
            
            # Test metrics recording
            metrics.record_http_request("GET", "/health", 200, 0.1)
            metrics.record_document_processed("resume", "success", 2.5)
            
            # Get metrics data
            metrics_data = metrics.get_metrics()
            
            if metrics_data:
                print("   ✅ Metrics system initialized")
                print("   📊 Prometheus metrics available at /metrics")
                self.setup_results["monitoring"] = True
                return True
            
            return False
            
        except Exception as e:
            print(f"   ❌ Monitoring setup failed: {e}")
            return False
    
    async def setup_background_tasks(self) -> bool:
        """Test background task system."""
        print("\n⚙️  Step 8: Background Tasks...")
        
        try:
            # Check if Redis is available
            import redis
            
            from app.core.config import get_settings
            settings = get_settings()
            
            # Test Redis connection
            redis_client = redis.from_url(settings.redis.url)
            redis_client.ping()
            print("   ✅ Redis connection successful")
            
            # Test Celery configuration
            from app.workers.celery_app import celery_app
            
            # Check if Celery app is configured
            if celery_app.conf.broker_url:
                print("   ✅ Celery configuration loaded")
                print("   💡 Start Celery worker with: celery -A app.workers.celery_app worker --loglevel=info")
                self.setup_results["background_tasks"] = True
                return True
            
            return False
            
        except ImportError:
            print("   ❌ Redis package not installed")
            return False
        except Exception as e:
            print(f"   ⚠️  Background tasks setup issue: {e}")
            print("   💡 Redis may not be running - install and start Redis server")
            # Don't fail completely - background tasks are optional for basic functionality
            self.setup_results["background_tasks"] = True
            return True
    
    async def run_tests(self) -> bool:
        """Run basic system tests."""
        print("\n🧪 Step 9: System Tests...")
        
        try:
            # Run basic health check
            from app.services.rag_engine import get_rag_engine
            
            rag_engine = get_rag_engine()
            health_status = await rag_engine.health_check()
            
            if health_status["rag_engine"] == "healthy":
                print("   ✅ RAG Engine health check passed")
            else:
                print(f"   ⚠️  RAG Engine health: {health_status['rag_engine']}")
            
            # Test document processing
            from app.services.document_processor import get_document_processor
            
            processor = get_document_processor()
            test_text = "This is a test document for processing validation."
            chunks = processor.create_text_chunks(test_text)
            
            if chunks:
                print("   ✅ Document processing test passed")
            
            self.setup_results["tests"] = True
            return True
            
        except Exception as e:
            print(f"   ❌ System tests failed: {e}")
            return False
    
    async def generate_setup_report(self) -> bool:
        """Generate final setup report."""
        print("\n" + "=" * 60)
        print("📋 SETUP REPORT")
        print("=" * 60)
        
        success_count = sum(1 for success in self.setup_results.values() if success)
        total_count = len(self.setup_results)
        
        for step, success in self.setup_results.items():
            status = "✅" if success else "❌"
            print(f"{status} {step.replace('_', ' ').title()}")
        
        print(f"\n📊 Overall: {success_count}/{total_count} steps completed")
        
        if success_count == total_count:
            print("\n🎉 PRODUCTION SETUP COMPLETED SUCCESSFULLY!")
            print("\n🚀 Next Steps:")
            print("   1. Start the production server: python production_server.py")
            print("   2. Visit API docs: http://localhost:8000/api/v1/docs")
            print("   3. Test <NAME_EMAIL> / admin123")
            print("   4. Check health: http://localhost:8000/health")
            print("   5. View metrics: http://localhost:8000/metrics")
            print("\n💡 Optional: Start Celery worker for background processing:")
            print("   celery -A app.workers.celery_app worker --loglevel=info")
            return True
        else:
            print("\n⚠️  SETUP COMPLETED WITH ISSUES")
            print("Please review the failed steps above and resolve any issues.")
            print("You may still be able to run basic functionality.")
            return False


async def main():
    """Main setup function."""
    setup = ProductionSetup()
    success = await setup.run_complete_setup()
    
    if not success:
        print("\n💡 For help:")
        print("   - Check the error messages above")
        print("   - Ensure PostgreSQL and Redis are installed and running")
        print("   - Verify your .env configuration")
        print("   - Check the project documentation")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
