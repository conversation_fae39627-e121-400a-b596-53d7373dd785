#!/usr/bin/env python3
"""
Ultra-simple server that just works for API documentation viewing.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set basic environment variables
os.environ.setdefault("DEBUG", "true")
os.environ.setdefault("ENVIRONMENT", "development")

try:
    # Try the minimal approach first
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
    import time
    
    # Create a simple FastAPI app
    app = FastAPI(
        title="Recruitment RAG System",
        version="1.0.0",
        description="AI-powered recruitment platform with RAG-based candidate scoring and personalized outreach",
        openapi_url="/api/v1/openapi.json",
        docs_url="/api/v1/docs",
        redoc_url="/api/v1/redoc",
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Import and include routes (with error handling)
    try:
        from app.api.v1 import api_router
        app.include_router(api_router, prefix="/api/v1")
    except Exception as e:
        print(f"⚠️  Could not load full API routes: {e}")
        print("📖 Basic documentation will still be available")
        
        # Create basic placeholder routes for documentation
        from fastapi import APIRouter
        
        placeholder_router = APIRouter()
        
        @placeholder_router.get("/candidates/", tags=["candidates"])
        async def list_candidates():
            """List all candidates with pagination and search."""
            return {"message": "Candidates endpoint - ready for implementation"}
        
        @placeholder_router.post("/candidates/", tags=["candidates"])
        async def create_candidate():
            """Create a new candidate profile."""
            return {"message": "Create candidate endpoint - ready for implementation"}
        
        @placeholder_router.post("/documents/upload", tags=["documents"])
        async def upload_document():
            """Upload and process candidate documents (PDF, DOCX, etc.)."""
            return {"message": "Document upload endpoint - ready for implementation"}
        
        @placeholder_router.post("/scoring/score", tags=["scoring"])
        async def score_candidate():
            """Score a candidate against job requirements using RAG."""
            return {"message": "RAG-based candidate scoring endpoint - ready for implementation"}
        
        @placeholder_router.post("/outreach/generate-email", tags=["outreach"])
        async def generate_email():
            """Generate personalized outreach email using RAG."""
            return {"message": "RAG-based email generation endpoint - ready for implementation"}
        
        @placeholder_router.post("/jobs/", tags=["jobs"])
        async def create_job():
            """Create a new job posting."""
            return {"message": "Job posting endpoint - ready for implementation"}
        
        app.include_router(placeholder_router, prefix="/api/v1")
    
    # Basic health check
    @app.get("/health")
    async def health_check():
        """System health check."""
        return {
            "status": "healthy",
            "service": "Recruitment RAG System",
            "version": "1.0.0",
            "timestamp": time.time(),
            "features": {
                "api_documentation": "✅ Available",
                "candidate_management": "🔨 Ready for implementation",
                "document_processing": "🔨 Ready for implementation", 
                "rag_scoring": "🔨 Ready for implementation",
                "email_generation": "🔨 Ready for implementation"
            }
        }
    
    @app.get("/")
    async def root():
        """Welcome endpoint."""
        return {
            "message": "Welcome to the Recruitment RAG System! 🚀",
            "documentation": "/api/v1/docs",
            "health": "/health",
            "description": "AI-powered recruitment with RAG-based candidate scoring and personalized outreach"
        }
    
    if __name__ == "__main__":
        print("🚀 Starting Recruitment RAG System...")
        print("📖 API Documentation: http://localhost:8000/api/v1/docs")
        print("🔍 Health Check: http://localhost:8000/health")
        print("📊 Alternative Docs: http://localhost:8000/api/v1/redoc")
        print("")
        
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )

except ImportError as e:
    print(f"❌ Missing dependencies: {e}")
    print("")
    print("🔧 Quick fix:")
    print("pip install fastapi uvicorn")
    print("")
    print("🔧 Or run the full setup:")
    print("python simple_start.py")
    sys.exit(1)
except Exception as e:
    print(f"❌ Failed to start server: {e}")
    sys.exit(1)
