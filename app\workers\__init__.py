"""
Celery workers package for background task processing.
Provides asynchronous processing for document handling, scoring, email generation, and maintenance.
"""

from app.workers.celery_app import celery_app

# Import all task modules to register tasks
from app.workers import (
    document_tasks,
    embedding_tasks,
    scoring_tasks,
    email_tasks,
    maintenance_tasks
)

# Export the Celery app for use in deployment
__all__ = [
    "celery_app",
    "document_tasks",
    "embedding_tasks", 
    "scoring_tasks",
    "email_tasks",
    "maintenance_tasks"
]

# Task registry for easy reference
AVAILABLE_TASKS = {
    # Document processing tasks
    "process_candidate_documents": document_tasks.process_candidate_documents_task,
    "process_single_document": document_tasks.process_single_document_task,
    "reprocess_document": document_tasks.reprocess_document_task,
    "bulk_document_processing": document_tasks.bulk_document_processing_task,
    "validate_document_format": document_tasks.validate_document_format_task,
    
    # Embedding tasks
    "generate_embeddings": embedding_tasks.generate_embeddings_task,
    "store_document_embeddings": embedding_tasks.store_document_embeddings_task,
    "reindex_candidate_embeddings": embedding_tasks.reindex_candidate_embeddings_task,
    "batch_embedding_generation": embedding_tasks.batch_embedding_generation_task,
    "cleanup_orphaned_embeddings": embedding_tasks.cleanup_orphaned_embeddings_task,
    "update_embedding_model": embedding_tasks.update_embedding_model_task,
    
    # Scoring tasks
    "score_candidate": scoring_tasks.score_candidate_task,
    "batch_score_candidates": scoring_tasks.batch_score_candidates_task,
    "rescore_candidate": scoring_tasks.rescore_candidate_task,
    "analyze_job_requirements": scoring_tasks.analyze_job_requirements_task,
    "compare_candidates": scoring_tasks.compare_candidates_task,
    "update_scoring_thresholds": scoring_tasks.update_scoring_thresholds_task,
    
    # Email tasks
    "generate_outreach_email": email_tasks.generate_outreach_email_task,
    "batch_generate_emails": email_tasks.batch_generate_emails_task,
    "regenerate_email": email_tasks.regenerate_email_task,
    "analyze_email_tone": email_tasks.analyze_email_tone_task,
    "optimize_email_subject_lines": email_tasks.optimize_email_subject_lines_task,
    "update_email_templates": email_tasks.update_email_templates_task,
    "analyze_email_performance": email_tasks.analyze_email_performance_task,
    
    # Maintenance tasks
    "cleanup_old_results": maintenance_tasks.cleanup_old_results_task,
    "update_vector_store_stats": maintenance_tasks.update_vector_store_stats_task,
    "monitor_system_health": maintenance_tasks.monitor_system_health_task,
    "cleanup_orphaned_files": maintenance_tasks.cleanup_orphaned_files_task,
    "optimize_vector_database": maintenance_tasks.optimize_vector_database_task,
    "backup_critical_data": maintenance_tasks.backup_critical_data_task,
    "generate_usage_report": maintenance_tasks.generate_usage_report_task,
    "update_search_indices": maintenance_tasks.update_search_indices_task,
    "check_api_quotas": maintenance_tasks.check_api_quotas_task,
}

# Queue configuration for different task types
QUEUE_CONFIGURATION = {
    "documents": {
        "description": "Document processing and text extraction",
        "tasks": [
            "process_candidate_documents",
            "process_single_document", 
            "reprocess_document",
            "bulk_document_processing",
            "validate_document_format"
        ],
        "concurrency": 2,
        "priority": "high"
    },
    "embeddings": {
        "description": "Embedding generation and vector operations",
        "tasks": [
            "generate_embeddings",
            "store_document_embeddings",
            "reindex_candidate_embeddings",
            "batch_embedding_generation",
            "cleanup_orphaned_embeddings",
            "update_embedding_model"
        ],
        "concurrency": 1,
        "priority": "medium"
    },
    "scoring": {
        "description": "Candidate scoring and evaluation",
        "tasks": [
            "score_candidate",
            "batch_score_candidates",
            "rescore_candidate",
            "analyze_job_requirements",
            "compare_candidates",
            "update_scoring_thresholds"
        ],
        "concurrency": 3,
        "priority": "high"
    },
    "emails": {
        "description": "Email generation and optimization",
        "tasks": [
            "generate_outreach_email",
            "batch_generate_emails",
            "regenerate_email",
            "analyze_email_tone",
            "optimize_email_subject_lines",
            "update_email_templates",
            "analyze_email_performance"
        ],
        "concurrency": 2,
        "priority": "medium"
    },
    "maintenance": {
        "description": "System maintenance and monitoring",
        "tasks": [
            "cleanup_old_results",
            "update_vector_store_stats",
            "monitor_system_health",
            "cleanup_orphaned_files",
            "optimize_vector_database",
            "backup_critical_data",
            "generate_usage_report",
            "update_search_indices",
            "check_api_quotas"
        ],
        "concurrency": 1,
        "priority": "low"
    }
}


def get_task_info(task_name: str) -> dict:
    """
    Get information about a specific task.
    
    Args:
        task_name: Name of the task
        
    Returns:
        dict: Task information including queue and priority
    """
    for queue_name, queue_info in QUEUE_CONFIGURATION.items():
        if task_name in queue_info["tasks"]:
            return {
                "task_name": task_name,
                "queue": queue_name,
                "priority": queue_info["priority"],
                "description": queue_info["description"],
                "function": AVAILABLE_TASKS.get(task_name)
            }
    
    return {"task_name": task_name, "error": "Task not found"}


def list_tasks_by_queue(queue_name: str = None) -> dict:
    """
    List tasks by queue or all tasks.
    
    Args:
        queue_name: Optional queue name to filter by
        
    Returns:
        dict: Tasks organized by queue
    """
    if queue_name:
        if queue_name in QUEUE_CONFIGURATION:
            return {queue_name: QUEUE_CONFIGURATION[queue_name]}
        else:
            return {"error": f"Queue '{queue_name}' not found"}
    
    return QUEUE_CONFIGURATION


def get_worker_startup_info() -> dict:
    """
    Get information for worker startup.
    
    Returns:
        dict: Worker configuration and task information
    """
    return {
        "total_tasks": len(AVAILABLE_TASKS),
        "queues": list(QUEUE_CONFIGURATION.keys()),
        "queue_configuration": QUEUE_CONFIGURATION,
        "available_tasks": list(AVAILABLE_TASKS.keys()),
        "celery_app": "app.workers.celery_app:celery_app"
    }
