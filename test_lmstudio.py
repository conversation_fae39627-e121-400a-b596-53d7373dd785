#!/usr/bin/env python3
"""
Test script for LM Studio integration with the RAG system.

This script tests both OpenAI and LM Studio providers to ensure
they work correctly with the RAG pipeline.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from app.services.llm_service import get_llm_service
from app.core.config import get_settings
import structlog

# Setup basic logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.dev.ConsoleRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")


async def test_provider_health(provider_name: str):
    """Test a specific provider's health."""
    print_section(f"Testing {provider_name} Provider Health")
    
    try:
        # Temporarily set the provider
        settings = get_settings()
        original_provider = settings.llm.llm_provider
        settings.llm.llm_provider = provider_name
        
        # Create new service instance with the provider
        llm_service = get_llm_service()
        
        # Test health check
        is_healthy = await llm_service.health_check()
        
        if is_healthy:
            print(f"✅ {provider_name} provider is healthy and ready!")
        else:
            print(f"❌ {provider_name} provider health check failed")
        
        # Restore original provider
        settings.llm.llm_provider = original_provider
        
        return is_healthy
        
    except Exception as e:
        print(f"❌ Error testing {provider_name} provider: {str(e)}")
        return False


async def test_text_generation(provider_name: str):
    """Test text generation with a specific provider."""
    print_section(f"Testing {provider_name} Text Generation")
    
    try:
        # Set provider
        settings = get_settings()
        original_provider = settings.llm.llm_provider
        settings.llm.llm_provider = provider_name
        
        # Create service instance
        llm_service = get_llm_service()
        
        # Test simple text generation
        prompt = "Write a brief professional summary for a software engineer with 5 years of Python experience."
        
        print(f"🔄 Generating text with {provider_name}...")
        print(f"📝 Prompt: {prompt}")
        
        result = await llm_service.provider.generate_text(
            prompt=prompt,
            max_tokens=150,
            temperature=0.1
        )
        
        print(f"✅ Generated text ({result['usage']['total_tokens']} tokens):")
        print(f"📄 {result['text']}")
        print(f"⏱️  Generation time: {result['generation_time_seconds']:.2f}s")
        print(f"🔧 Model: {result['model']}")
        
        # Restore original provider
        settings.llm.llm_provider = original_provider
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing {provider_name} text generation: {str(e)}")
        return False


async def test_embeddings(provider_name: str):
    """Test embedding generation with a specific provider."""
    print_section(f"Testing {provider_name} Embeddings")
    
    try:
        # Set provider
        settings = get_settings()
        original_provider = settings.llm.llm_provider
        settings.llm.llm_provider = provider_name
        
        # Create service instance
        llm_service = get_llm_service()
        
        # Test embedding generation
        texts = [
            "Senior software engineer with Python experience",
            "Junior developer looking for opportunities",
            "Data scientist with machine learning expertise"
        ]
        
        print(f"🔄 Generating embeddings for {len(texts)} texts...")
        
        embeddings = await llm_service.generate_embeddings(texts)
        
        print(f"✅ Generated {len(embeddings)} embeddings")
        print(f"📐 Embedding dimensions: {len(embeddings[0]) if embeddings else 'N/A'}")
        print(f"📊 Sample embedding (first 5 values): {embeddings[0][:5] if embeddings else 'N/A'}")
        
        # Restore original provider
        settings.llm.llm_provider = original_provider
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing {provider_name} embeddings: {str(e)}")
        return False


async def test_candidate_scoring(provider_name: str):
    """Test candidate scoring with a specific provider."""
    print_section(f"Testing {provider_name} Candidate Scoring")
    
    try:
        # Set provider
        settings = get_settings()
        original_provider = settings.llm.llm_provider
        settings.llm.llm_provider = provider_name
        
        # Create service instance
        llm_service = get_llm_service()
        
        # Sample data
        job_description = """
        We are looking for a Senior Python Developer with 3+ years of experience.
        Required skills: Python, FastAPI, PostgreSQL, Docker, AWS.
        Experience with AI/ML is a plus.
        """
        
        candidate_data = """
        John Smith - Senior Software Engineer
        5 years of Python development experience
        Proficient in FastAPI, Django, PostgreSQL
        Experience with Docker and AWS deployment
        Built several machine learning projects
        """
        
        evidence_snippets = [
            "5 years Python development experience",
            "FastAPI and Django expertise",
            "PostgreSQL database skills",
            "Docker containerization experience",
            "AWS cloud deployment",
            "Machine learning project portfolio"
        ]
        
        print(f"🔄 Scoring candidate with {provider_name}...")
        
        result = await llm_service.generate_candidate_score(
            job_description=job_description,
            candidate_data=candidate_data,
            evidence_snippets=evidence_snippets
        )
        
        print(f"✅ Generated candidate score!")
        print(f"📄 Response preview: {result['text'][:200]}...")
        print(f"🔧 Model: {result['model']}")
        print(f"⏱️  Generation time: {result['generation_time_seconds']:.2f}s")
        
        # Restore original provider
        settings.llm.llm_provider = original_provider
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing {provider_name} candidate scoring: {str(e)}")
        return False


def show_configuration():
    """Show current configuration."""
    print_section("Current Configuration")
    
    settings = get_settings()
    
    print(f"🔧 Current LLM Provider: {settings.llm.llm_provider}")
    print(f"🌐 OpenAI API Key: {'Set' if settings.llm.openai_api_key and settings.llm.openai_api_key != 'your-openai-api-key-here' else 'Not Set'}")
    print(f"🏠 LM Studio Host: {settings.llm.lmstudio_host}")
    print(f"🤖 LM Studio Model: {settings.llm.lmstudio_model}")


async def main():
    """Main test function."""
    print_section("🚀 RAG System LM Studio Integration Test")
    
    print("This script tests the integration between your RAG system and:")
    print("  • OpenAI GPT models (cloud-based)")
    print("  • LM Studio (local models)")
    
    show_configuration()
    
    # Test available providers
    providers_to_test = ["openai", "lmstudio"]
    
    for provider in providers_to_test:
        print(f"\n{'🔵 Testing ' + provider.upper() + ' Provider'}")
        
        # Health check
        health_ok = await test_provider_health(provider)
        if not health_ok:
            print(f"⏭️  Skipping {provider} tests due to health check failure")
            continue
        
        # Text generation
        text_ok = await test_text_generation(provider)
        
        # Embeddings
        embed_ok = await test_embeddings(provider)
        
        # Candidate scoring
        score_ok = await test_candidate_scoring(provider)
        
        # Summary
        total_tests = 4
        passed_tests = sum([health_ok, text_ok, embed_ok, score_ok])
        
        print(f"\n📊 {provider.upper()} Results: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print(f"✅ {provider.upper()} provider is fully functional!")
        elif passed_tests > 0:
            print(f"⚠️  {provider.upper()} provider is partially functional")
        else:
            print(f"❌ {provider.upper()} provider is not working")
    
    print_section("🎯 Setup Instructions")
    
    print("To use LM Studio with your RAG system:")
    print("1. Download and install LM Studio from: https://lmstudio.ai/")
    print("2. Download a model (recommended: Llama 3.1, Mistral, or Code Llama)")
    print("3. Start the LM Studio server (usually on http://localhost:1234)")
    print("4. Update your .env file:")
    print("   LLM_PROVIDER=lmstudio")
    print("   LLM_LMSTUDIO_HOST=http://localhost:1234")
    print("   LLM_LMSTUDIO_MODEL=your-model-name")
    print("5. Restart your RAG system")
    
    print("\nTo switch back to OpenAI:")
    print("1. Set your OpenAI API key in .env:")
    print("   LLM_OPENAI_API_KEY=your-actual-api-key")
    print("2. Update your .env file:")
    print("   LLM_PROVIDER=openai")
    print("3. Restart your RAG system")
    
    print_section("✨ Test Complete")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
