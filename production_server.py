#!/usr/bin/env python3
"""
Production-ready RAG API server with authentication, monitoring, and background processing.
Integrates all Phase 2 enhancements: database, auth, metrics, and Celery.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer
from contextlib import asynccontextmanager
import structlog
import time
import uuid

# Import our enhanced components
from app.core.config import get_settings
from app.core.logging import setup_logging
from app.core.exceptions import RAGException
from app.db.database import init_db, close_db, get_db_manager
from app.services.metrics import get_metrics, MetricsMiddleware
from app.services.auth_service import get_auth_service
from app.api.v1 import api_router

# Setup logging
logger = structlog.get_logger(__name__)
security = HTTPBearer()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Enhanced application lifespan manager.
    Handles startup and shutdown with all production services.
    """
    settings = get_settings()
    metrics = get_metrics()
    
    # Startup
    logger.info("🚀 Starting Production RAG System", version=settings.app_version)
    
    # Initialize database
    try:
        await init_db()
        logger.info("✅ Database initialized")
        
        # Test database connection
        db_manager = get_db_manager()
        db_healthy = await db_manager.health_check()
        if db_healthy:
            logger.info("✅ Database health check passed")
        else:
            logger.warning("⚠️ Database health check failed")
        
    except Exception as e:
        logger.error("❌ Database initialization failed", error=str(e))
        raise
    
    # Initialize metrics
    try:
        metrics.update_active_users(0)
        logger.info("✅ Metrics system initialized")
    except Exception as e:
        logger.warning("⚠️ Metrics initialization failed", error=str(e))
    
    # Test core RAG services
    try:
        from app.services.rag_engine import get_rag_engine
        rag_engine = get_rag_engine()
        
        # Perform health check
        health_status = await rag_engine.health_check()
        if health_status["rag_engine"] == "healthy":
            logger.info("✅ RAG Engine health check passed")
        else:
            logger.warning("⚠️ RAG Engine health check failed", status=health_status)
            
    except Exception as e:
        logger.warning("⚠️ RAG Engine health check failed", error=str(e))
    
    logger.info("🎉 Production RAG System startup completed!")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down Production RAG System")
    try:
        await close_db()
        logger.info("✅ Database connections closed")
    except Exception as e:
        logger.warning("⚠️ Database shutdown failed", error=str(e))


def create_production_app() -> FastAPI:
    """
    Create and configure the production FastAPI application.
    
    Returns:
        FastAPI: Configured production application instance
    """
    settings = get_settings()
    
    # Setup logging
    try:
        setup_logging(
            level=settings.monitoring.log_level,
            format_type=settings.monitoring.log_format
        )
    except Exception as e:
        print(f"❌ Logging setup failed: {e}")
    
    # Create FastAPI instance with production settings
    app = FastAPI(
        title=f"{settings.app_name} - Production API",
        version=settings.app_version,
        description="""
        **Production RAG System for Intelligent Recruitment**
        
        🔥 **New Production Features:**
        - ✅ **User Authentication** - JWT-based secure access
        - ✅ **Database Integration** - PostgreSQL with migrations
        - ✅ **Background Processing** - Celery task queue
        - ✅ **Monitoring & Metrics** - Prometheus metrics
        - ✅ **Production Security** - Rate limiting, validation
        
        🚀 **Core RAG Pipeline:**
        - Document processing (PDF, DOCX, TXT)
        - Vector similarity search (ChromaDB)
        - AI-powered candidate scoring (GPT-4)
        - Personalized email generation
        
        📖 **Authentication:**
        - Register: `POST /api/v1/auth/register`
        - Login: `POST /api/v1/auth/login`
        - Use Bearer token for protected endpoints
        
        📊 **Monitoring:**
        - Health: `GET /health`
        - Metrics: `GET /metrics` (Prometheus format)
        """,
        debug=settings.debug,
        lifespan=lifespan,
        openapi_url=f"{settings.api_prefix}/openapi.json" if settings.debug else None,
        docs_url=f"{settings.api_prefix}/docs" if settings.debug else None,
        redoc_url=f"{settings.api_prefix}/redoc" if settings.debug else None,
    )
    
    # Add production middleware
    setup_production_middleware(app, settings)
    
    # Add exception handlers
    setup_enhanced_exception_handlers(app)
    
    # Include API routes
    app.include_router(
        api_router,
        prefix=settings.api_prefix
    )
    
    # Add production endpoints
    setup_production_endpoints(app)
    
    return app


def setup_production_middleware(app: FastAPI, settings) -> None:
    """
    Configure production middleware stack.
    
    Args:
        app: FastAPI application instance
        settings: Application settings
    """
    
    # Metrics middleware (first, to capture all requests)
    app.add_middleware(MetricsMiddleware)
    
    # Request ID and logging middleware
    @app.middleware("http")
    async def enhanced_logging_middleware(request, call_next):
        """Enhanced logging with metrics and security."""
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        # Add to request state
        request.state.request_id = request_id
        
        # Security headers
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # Log request start
        try:
            logger.info(
                "Request started",
                request_id=request_id,
                method=request.method,
                url=str(request.url),
                client_ip=client_ip,
                user_agent=user_agent[:100]  # Truncate long user agents
            )
        except:
            pass
        
        # Process request
        response = await call_next(request)
        
        # Calculate metrics
        duration = time.time() - start_time
        
        # Add security headers
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        
        # Log request completion
        try:
            logger.info(
                "Request completed",
                request_id=request_id,
                status_code=response.status_code,
                duration=duration,
                client_ip=client_ip
            )
        except:
            pass
        
        return response
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.debug else ["https://yourdomain.com"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Trusted host middleware (production security)
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["yourdomain.com", "*.yourdomain.com", "localhost"]
        )


def setup_enhanced_exception_handlers(app: FastAPI) -> None:
    """
    Setup enhanced exception handlers with metrics.
    
    Args:
        app: FastAPI application instance
    """
    metrics = get_metrics()
    
    @app.exception_handler(RAGException)
    async def rag_exception_handler(request, exc: RAGException):
        """Handle custom RAG exceptions with metrics."""
        request_id = getattr(request.state, "request_id", None)
        
        # Record error metric
        metrics.record_error("rag_exception", "api")
        
        try:
            logger.error(
                "RAG Exception",
                error_type=type(exc).__name__,
                error_message=str(exc),
                request_id=request_id,
                url=str(request.url)
            )
        except:
            pass
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.error_type,
                "message": exc.message,
                "request_id": request_id,
                "timestamp": time.time()
            }
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc: HTTPException):
        """Handle HTTP exceptions with metrics."""
        request_id = getattr(request.state, "request_id", None)
        
        # Record error metric for 4xx and 5xx errors
        if exc.status_code >= 400:
            error_type = "client_error" if exc.status_code < 500 else "server_error"
            metrics.record_error(error_type, "api")
        
        try:
            logger.warning(
                "HTTP Exception",
                status_code=exc.status_code,
                detail=exc.detail,
                request_id=request_id,
                url=str(request.url)
            )
        except:
            pass
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": "HTTP Error",
                "message": exc.detail,
                "request_id": request_id,
                "timestamp": time.time()
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc: Exception):
        """Handle unexpected exceptions with metrics."""
        request_id = getattr(request.state, "request_id", None)
        
        # Record error metric
        metrics.record_error("unexpected_error", "api")
        
        try:
            logger.error(
                "Unexpected Exception",
                error_type=type(exc).__name__,
                error_message=str(exc),
                request_id=request_id,
                url=str(request.url),
                exc_info=True
            )
        except:
            pass
        
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An unexpected error occurred",
                "request_id": request_id,
                "timestamp": time.time()
            }
        )


def setup_production_endpoints(app: FastAPI) -> None:
    """
    Setup production-specific endpoints.
    
    Args:
        app: FastAPI application instance
    """
    
    @app.get("/health")
    async def enhanced_health_check():
        """Enhanced health check with detailed component status."""
        try:
            health_status = {
                "status": "healthy",
                "timestamp": time.time(),
                "environment": get_settings().environment,
                "version": get_settings().app_version,
                "components": {}
            }
            
            # Database health
            try:
                db_manager = get_db_manager()
                db_healthy = await db_manager.health_check()
                health_status["components"]["database"] = {
                    "status": "healthy" if db_healthy else "unhealthy",
                    "type": "postgresql"
                }
            except Exception as e:
                health_status["components"]["database"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "type": "postgresql"
                }
            
            # RAG Engine health
            try:
                from app.services.rag_engine import get_rag_engine
                rag_engine = get_rag_engine()
                rag_health = await rag_engine.health_check()
                health_status["components"]["rag_engine"] = rag_health["components"]
            except Exception as e:
                health_status["components"]["rag_engine"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
            
            # Determine overall health
            component_statuses = []
            for comp_name, comp_status in health_status["components"].items():
                if isinstance(comp_status, dict):
                    if "status" in comp_status:
                        component_statuses.append(comp_status["status"])
                    else:
                        # For nested components (like rag_engine)
                        for sub_comp in comp_status.values():
                            if isinstance(sub_comp, dict) and "status" in sub_comp:
                                component_statuses.append(sub_comp["status"])
            
            if all(status == "healthy" for status in component_statuses):
                health_status["status"] = "healthy"
            elif any(status == "healthy" for status in component_statuses):
                health_status["status"] = "degraded"
            else:
                health_status["status"] = "unhealthy"
            
            status_code = 200 if health_status["status"] in ["healthy", "degraded"] else 503
            
            return JSONResponse(
                status_code=status_code,
                content=health_status
            )
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": time.time()
                }
            )
    
    @app.get("/metrics")
    async def get_metrics():
        """Get Prometheus metrics."""
        metrics = get_metrics()
        return metrics.get_metrics_response()
    
    @app.get("/info")
    async def get_system_info():
        """Get system information."""
        settings = get_settings()
        
        return {
            "application": {
                "name": settings.app_name,
                "version": settings.app_version,
                "environment": settings.environment,
                "debug": settings.debug
            },
            "features": {
                "authentication": True,
                "background_processing": True,
                "metrics": True,
                "vector_search": True,
                "document_processing": True,
                "email_generation": True
            },
            "endpoints": {
                "docs": f"{settings.api_prefix}/docs" if settings.debug else None,
                "health": "/health",
                "metrics": "/metrics"
            }
        }


# Create application instance
app = create_production_app()

if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    # Startup messages
    print("🚀 Starting Production RAG System")
    print("=" * 50)
    print(f"📖 API Documentation: http://localhost:{settings.api_port}{settings.api_prefix}/docs")
    print(f"🏥 Health Check: http://localhost:{settings.api_port}/health")
    print(f"📊 Metrics: http://localhost:{settings.api_port}/metrics")
    print(f"ℹ️  System Info: http://localhost:{settings.api_port}/info")
    print("=" * 50)
    
    # Check critical configuration
    try:
        if not settings.llm.openai_api_key or settings.llm.openai_api_key == "your-openai-api-key-here":
            print("⚠️  WARNING: OpenAI API key not configured!")
            print("Please set OPENAI_API_KEY in your .env file")
    except Exception as e:
        print(f"⚠️  Configuration issue: {e}")
    
    # Start server
    uvicorn.run(
        "production_server:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.monitoring.log_level.lower(),
        access_log=True
    )
