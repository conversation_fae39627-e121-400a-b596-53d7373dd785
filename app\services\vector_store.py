"""
Vector Store Service for handling vector similarity search.
Provides abstraction layer for ChromaDB, Pinecone, and other vector databases.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
import chromadb
from chromadb.config import Settings
import structlog
from abc import ABC, abstractmethod
import uuid
import numpy as np

from app.core.config import get_settings
from app.core.exceptions import VectorStoreError, ConfigurationError
from app.core.logging import get_rag_logger

logger = get_rag_logger(__name__)


class BaseVectorStore(ABC):
    """
    Abstract base class for vector store providers.
    """
    
    @abstractmethod
    async def add_documents(
        self,
        documents: List[Dict[str, Any]],
        embeddings: List[List[float]],
        collection_name: str = "default"
    ) -> List[str]:
        """Add documents with embeddings to the vector store."""
        pass
    
    @abstractmethod
    async def search(
        self,
        query_embedding: List[float],
        collection_name: str = "default",
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar documents."""
        pass
    
    @abstractmethod
    async def delete_documents(
        self,
        document_ids: List[str],
        collection_name: str = "default"
    ) -> bool:
        """Delete documents from the vector store."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the vector store is healthy."""
        pass


class ChromaDBProvider(BaseVectorStore):
    """
    ChromaDB provider for local vector storage.
    """
    
    def __init__(self, persist_directory: str):
        self.persist_directory = persist_directory
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        self.collections = {}
    
    def _get_or_create_collection(self, collection_name: str):
        """Get or create a ChromaDB collection."""
        if collection_name not in self.collections:
            try:
                # Try to get existing collection
                collection = self.client.get_collection(collection_name)
            except Exception:
                # Create new collection if it doesn't exist
                collection = self.client.create_collection(
                    name=collection_name,
                    metadata={"hnsw:space": "cosine"}
                )
            
            self.collections[collection_name] = collection
        
        return self.collections[collection_name]
    
    async def add_documents(
        self,
        documents: List[Dict[str, Any]],
        embeddings: List[List[float]],
        collection_name: str = "default"
    ) -> List[str]:
        """
        Add documents with embeddings to ChromaDB.
        
        Args:
            documents: List of document metadata
            embeddings: List of embedding vectors
            collection_name: Collection name
            
        Returns:
            List[str]: Document IDs
        """
        start_time = time.time()
        
        try:
            collection = self._get_or_create_collection(collection_name)
            
            # Generate IDs if not provided
            document_ids = []
            document_texts = []
            metadatas = []
            
            for doc in documents:
                doc_id = doc.get("id", str(uuid.uuid4()))
                document_ids.append(doc_id)
                document_texts.append(doc.get("content", ""))
                
                # Prepare metadata (ChromaDB doesn't allow nested objects)
                metadata = {
                    "document_id": doc.get("document_id", ""),
                    "chunk_index": doc.get("chunk_index", 0),
                    "candidate_id": doc.get("candidate_id", ""),
                    "document_type": doc.get("document_type", ""),
                    "source": doc.get("source", ""),
                    "page_number": doc.get("page_number", 0),
                    "content_length": len(doc.get("content", ""))
                }
                metadatas.append(metadata)
            
            # Add to collection
            collection.add(
                ids=document_ids,
                embeddings=embeddings,
                documents=document_texts,
                metadatas=metadatas
            )
            
            processing_time = time.time() - start_time
            
            logger.info(
                "Documents added to vector store",
                collection=collection_name,
                document_count=len(documents),
                processing_time_seconds=processing_time
            )
            
            return document_ids
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Failed to add documents to ChromaDB: {str(e)}"
            
            logger.error(
                "Vector store add failed",
                collection=collection_name,
                document_count=len(documents),
                processing_time_seconds=processing_time,
                error=error_msg
            )
            
            raise VectorStoreError(error_msg, operation="add_documents")
    
    async def search(
        self,
        query_embedding: List[float],
        collection_name: str = "default",
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar documents in ChromaDB.
        
        Args:
            query_embedding: Query embedding vector
            collection_name: Collection name
            top_k: Number of results to return
            filters: Optional metadata filters
            
        Returns:
            List[Dict]: Search results with metadata
        """
        start_time = time.time()
        
        try:
            collection = self._get_or_create_collection(collection_name)
            
            # Prepare where filter for ChromaDB
            where_filter = None
            if filters:
                where_filter = {}
                for key, value in filters.items():
                    if isinstance(value, (str, int, float, bool)):
                        where_filter[key] = value
            
            # Perform search
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=where_filter,
                include=["metadatas", "documents", "distances"]
            )
            
            # Format results
            formatted_results = []
            if results["ids"] and results["ids"][0]:
                for i in range(len(results["ids"][0])):
                    result = {
                        "id": results["ids"][0][i],
                        "content": results["documents"][0][i],
                        "metadata": results["metadatas"][0][i],
                        "similarity_score": 1.0 - results["distances"][0][i],  # Convert distance to similarity
                        "distance": results["distances"][0][i]
                    }
                    formatted_results.append(result)
            
            search_time = time.time() - start_time
            
            logger.log_vector_search(
                query="embedding_search",
                results_count=len(formatted_results),
                search_time=search_time
            )
            
            return formatted_results
            
        except Exception as e:
            search_time = time.time() - start_time
            error_msg = f"Failed to search ChromaDB: {str(e)}"
            
            logger.log_vector_search(
                query="embedding_search",
                results_count=0,
                search_time=search_time,
                error=error_msg
            )
            
            raise VectorStoreError(error_msg, operation="search")
    
    async def delete_documents(
        self,
        document_ids: List[str],
        collection_name: str = "default"
    ) -> bool:
        """
        Delete documents from ChromaDB.
        
        Args:
            document_ids: List of document IDs to delete
            collection_name: Collection name
            
        Returns:
            bool: Success status
        """
        try:
            collection = self._get_or_create_collection(collection_name)
            collection.delete(ids=document_ids)
            
            logger.info(
                "Documents deleted from vector store",
                collection=collection_name,
                document_count=len(document_ids)
            )
            
            return True
            
        except Exception as e:
            error_msg = f"Failed to delete documents from ChromaDB: {str(e)}"
            logger.error(error_msg)
            raise VectorStoreError(error_msg, operation="delete_documents")
    
    async def health_check(self) -> bool:
        """
        Check ChromaDB health.
        
        Returns:
            bool: True if healthy
        """
        try:
            # Simple test operation
            test_collection = self._get_or_create_collection("health_check")
            return True
        except Exception as e:
            logger.error(f"ChromaDB health check failed: {str(e)}")
            return False
    
    async def get_collection_stats(self, collection_name: str = "default") -> Dict[str, Any]:
        """
        Get collection statistics.
        
        Args:
            collection_name: Collection name
            
        Returns:
            dict: Collection statistics
        """
        try:
            collection = self._get_or_create_collection(collection_name)
            count = collection.count()
            
            return {
                "collection_name": collection_name,
                "document_count": count,
                "status": "healthy"
            }
            
        except Exception as e:
            return {
                "collection_name": collection_name,
                "document_count": 0,
                "status": "error",
                "error": str(e)
            }


class VectorStoreService:
    """
    Main vector store service that manages different providers.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.provider = self._initialize_provider()
    
    def _initialize_provider(self) -> BaseVectorStore:
        """Initialize the configured vector store provider."""
        
        # Primary: ChromaDB (local)
        if self.settings.vectordb.chroma_persist_directory:
            return ChromaDBProvider(
                persist_directory=self.settings.vectordb.chroma_persist_directory
            )
        
        # TODO: Add Pinecone provider for production scaling
        
        raise ConfigurationError(
            "No vector store configured. Please set ChromaDB persist directory.",
            config_key="chroma_persist_directory"
        )
    
    async def add_candidate_documents(
        self,
        candidate_id: str,
        documents: List[Dict[str, Any]],
        embeddings: List[List[float]]
    ) -> List[str]:
        """
        Add candidate documents to vector store.
        
        Args:
            candidate_id: Candidate UUID
            documents: Document chunks with metadata
            embeddings: Corresponding embeddings
            
        Returns:
            List[str]: Document IDs
        """
        # Add candidate_id to all documents
        for doc in documents:
            doc["candidate_id"] = candidate_id
        
        return await self.provider.add_documents(
            documents=documents,
            embeddings=embeddings,
            collection_name="candidates"
        )
    
    async def search_candidate_evidence(
        self,
        query_embedding: List[float],
        candidate_id: Optional[str] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant candidate evidence.
        
        Args:
            query_embedding: Query embedding
            candidate_id: Optional candidate filter
            top_k: Number of results
            similarity_threshold: Minimum similarity score
            
        Returns:
            List[Dict]: Relevant evidence chunks
        """
        filters = {}
        if candidate_id:
            filters["candidate_id"] = candidate_id
        
        results = await self.provider.search(
            query_embedding=query_embedding,
            collection_name="candidates",
            top_k=top_k,
            filters=filters
        )
        
        # Filter by similarity threshold
        filtered_results = [
            result for result in results
            if result["similarity_score"] >= similarity_threshold
        ]
        
        return filtered_results
    
    async def delete_candidate_documents(self, candidate_id: str) -> bool:
        """
        Delete all documents for a candidate.
        
        Args:
            candidate_id: Candidate UUID
            
        Returns:
            bool: Success status
        """
        try:
            # First, search for all documents for this candidate
            dummy_embedding = [0.0] * 1536  # OpenAI embedding dimension
            results = await self.provider.search(
                query_embedding=dummy_embedding,
                collection_name="candidates",
                top_k=10000,  # Large number to get all
                filters={"candidate_id": candidate_id}
            )
            
            if results:
                document_ids = [result["id"] for result in results]
                return await self.provider.delete_documents(
                    document_ids=document_ids,
                    collection_name="candidates"
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete candidate documents: {str(e)}")
            return False
    
    async def health_check(self) -> bool:
        """Check vector store health."""
        return await self.provider.health_check()
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics."""
        if hasattr(self.provider, "get_collection_stats"):
            return await self.provider.get_collection_stats("candidates")
        return {"status": "stats_not_available"}


# Global service instance
_vector_store_service: Optional[VectorStoreService] = None


def get_vector_store() -> VectorStoreService:
    """
    Get or create vector store service instance.
    
    Returns:
        VectorStoreService: Service instance
    """
    global _vector_store_service
    
    if _vector_store_service is None:
        _vector_store_service = VectorStoreService()
    
    return _vector_store_service
