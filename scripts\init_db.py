#!/usr/bin/env python3
"""
Database initialization script for RAG system.
Creates tables and optionally seeds with sample data.
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Add app directory to path
sys.path.append(str(Path(__file__).parent.parent))

from app.db.database import init_db, close_db, get_db_manager
from app.core.config import get_settings
from app.models import *  # Import all models


async def create_tables():
    """Create all database tables."""
    print("Creating database tables...")
    await init_db()
    print("✅ Database tables created successfully")


async def seed_sample_data():
    """Seed database with sample data for development."""
    print("Seeding sample data...")
    
    # TODO: Implement sample data creation
    # This would create sample users, candidates, jobs, etc.
    
    print("✅ Sample data seeded successfully")


async def reset_database():
    """Reset database by dropping and recreating all tables."""
    print("⚠️  WARNING: This will delete all data!")
    confirm = input("Are you sure you want to reset the database? (yes/no): ")
    
    if confirm.lower() != 'yes':
        print("Database reset cancelled")
        return
    
    from app.db.database import Base, engine
    
    print("Dropping all tables...")
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    print("Creating new tables...")
    await create_tables()
    
    print("✅ Database reset completed")


async def check_database_health():
    """Check database connection and health."""
    print("Checking database health...")
    
    db_manager = get_db_manager()
    is_healthy = await db_manager.health_check()
    
    if is_healthy:
        print("✅ Database is healthy and accessible")
    else:
        print("❌ Database health check failed")
        sys.exit(1)


async def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(description="RAG System Database Manager")
    parser.add_argument(
        "command",
        choices=["init", "seed", "reset", "health"],
        help="Command to execute"
    )
    
    args = parser.parse_args()
    
    settings = get_settings()
    print(f"Using database: {settings.database.url}")
    
    try:
        if args.command == "init":
            await create_tables()
        elif args.command == "seed":
            await create_tables()
            await seed_sample_data()
        elif args.command == "reset":
            await reset_database()
        elif args.command == "health":
            await check_database_health()
    
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)
    
    finally:
        await close_db()


if __name__ == "__main__":
    asyncio.run(main())
