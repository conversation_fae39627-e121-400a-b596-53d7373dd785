# =============================================================================
# RAG System - Ultra-Minimal Dependencies (Python 3.13 Compatible)
# =============================================================================

# Core Web Framework
fastapi>=0.100.0
uvicorn>=0.20.0

# Basic dependencies that should work
python-dotenv>=1.0.0
structlog>=23.0.0
click>=8.0.0

# Document processing (simple)
python-docx>=1.0.0
python-multipart>=0.0.6

# HTTP client
httpx>=0.24.0
requests>=2.30.0

# Basic testing
pytest>=7.0.0
