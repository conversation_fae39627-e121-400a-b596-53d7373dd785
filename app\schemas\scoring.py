"""
Scoring schemas for RAG-based candidate evaluation.
"""

from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from uuid import UUID
from datetime import datetime


class RequirementScore(BaseModel):
    """Schema for individual requirement scoring."""
    requirement: str
    evidence: List[str]  # Evidence snippets from candidate data
    score: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    explanation: str


class ScoringRequest(BaseModel):
    """Schema for candidate scoring request."""
    candidate_id: UUID
    job_id: UUID
    custom_requirements: Optional[List[str]] = None
    weighting_preferences: Optional[Dict[str, float]] = None
    include_explanation: bool = True


class ScoringResponse(BaseModel):
    """Schema for candidate scoring response."""
    id: UUID
    candidate_id: UUID
    job_id: UUID
    overall_score: float  # 0.0 to 1.0
    overall_percentage: int  # 0 to 100
    fit_level: str  # "excellent", "good", "fair", "poor"
    
    # Detailed scoring breakdown
    requirement_scores: List[RequirementScore]
    notable_strengths: List[str]
    potential_gaps: List[str]
    
    # Evidence and reasoning
    key_evidence: List[str]  # Top evidence supporting the score
    reasoning_summary: str
    
    # Metadata
    total_evidence_chunks: int
    processing_time_seconds: float
    model_used: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class BatchScoringRequest(BaseModel):
    """Schema for batch candidate scoring."""
    candidate_ids: List[UUID]
    job_id: UUID
    custom_requirements: Optional[List[str]] = None
    weighting_preferences: Optional[Dict[str, float]] = None
    include_explanation: bool = True


class ScoringComparison(BaseModel):
    """Schema for comparing multiple candidate scores."""
    job_id: UUID
    candidates: List[ScoringResponse]
    ranking: List[UUID]  # Candidate IDs in ranked order
    comparison_insights: List[str]
    generated_at: datetime


class ScoringHistory(BaseModel):
    """Schema for scoring history entry."""
    id: UUID
    candidate_id: UUID
    job_id: UUID
    job_title: str
    company: str
    overall_score: float
    fit_level: str
    created_at: datetime


class ScoreExplanation(BaseModel):
    """Schema for detailed score explanation."""
    score_id: UUID
    reasoning_steps: List[str]
    evidence_analysis: Dict[str, Any]
    alternative_interpretations: Optional[List[str]] = None
    confidence_factors: Dict[str, float]
    improvement_suggestions: List[str]
