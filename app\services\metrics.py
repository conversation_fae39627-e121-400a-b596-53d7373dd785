"""
Prometheus metrics for RAG system monitoring.
Provides application metrics, performance tracking, and health indicators.
"""

import time
from typing import Dict, Any, Optional
from prometheus_client import (
    Counter, Histogram, Gauge, Info, 
    CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
)
from fastapi import Response
import structlog

logger = structlog.get_logger(__name__)

# Create custom registry for metrics
registry = CollectorRegistry()

# =============================================================================
# Application Metrics
# =============================================================================

# Request metrics
http_requests_total = Counter(
    "rag_http_requests_total",
    "Total number of HTTP requests",
    ["method", "endpoint", "status_code"],
    registry=registry
)

http_request_duration_seconds = Histogram(
    "rag_http_request_duration_seconds",
    "HTTP request duration in seconds",
    ["method", "endpoint"],
    registry=registry
)

# RAG Pipeline metrics
documents_processed_total = Counter(
    "rag_documents_processed_total",
    "Total number of documents processed",
    ["document_type", "status"],
    registry=registry
)

document_processing_duration_seconds = Histogram(
    "rag_document_processing_duration_seconds",
    "Document processing duration in seconds",
    ["document_type"],
    registry=registry
)

embeddings_generated_total = Counter(
    "rag_embeddings_generated_total",
    "Total number of embeddings generated",
    ["model"],
    registry=registry
)

vector_searches_total = Counter(
    "rag_vector_searches_total",
    "Total number of vector searches performed",
    ["collection"],
    registry=registry
)

vector_search_duration_seconds = Histogram(
    "rag_vector_search_duration_seconds",
    "Vector search duration in seconds",
    ["collection"],
    registry=registry
)

candidate_scores_generated_total = Counter(
    "rag_candidate_scores_generated_total",
    "Total number of candidate scores generated",
    ["fit_level"],
    registry=registry
)

candidate_scoring_duration_seconds = Histogram(
    "rag_candidate_scoring_duration_seconds",
    "Candidate scoring duration in seconds",
    [],
    registry=registry
)

emails_generated_total = Counter(
    "rag_emails_generated_total",
    "Total number of outreach emails generated",
    ["tone"],
    registry=registry
)

email_generation_duration_seconds = Histogram(
    "rag_email_generation_duration_seconds",
    "Email generation duration in seconds",
    ["tone"],
    registry=registry
)

# LLM metrics
llm_requests_total = Counter(
    "rag_llm_requests_total",
    "Total number of LLM API requests",
    ["model", "operation", "status"],
    registry=registry
)

llm_tokens_consumed_total = Counter(
    "rag_llm_tokens_consumed_total",
    "Total number of LLM tokens consumed",
    ["model", "token_type"],
    registry=registry
)

llm_request_duration_seconds = Histogram(
    "rag_llm_request_duration_seconds",
    "LLM request duration in seconds",
    ["model", "operation"],
    registry=registry
)

# System metrics
active_users_gauge = Gauge(
    "rag_active_users",
    "Number of currently active users",
    registry=registry
)

vector_store_documents_gauge = Gauge(
    "rag_vector_store_documents_total",
    "Total number of documents in vector store",
    ["collection"],
    registry=registry
)

database_connections_gauge = Gauge(
    "rag_database_connections_active",
    "Number of active database connections",
    registry=registry
)

# Background task metrics
celery_tasks_total = Counter(
    "rag_celery_tasks_total",
    "Total number of Celery tasks",
    ["task_name", "status"],
    registry=registry
)

celery_task_duration_seconds = Histogram(
    "rag_celery_task_duration_seconds",
    "Celery task duration in seconds",
    ["task_name"],
    registry=registry
)

# Error metrics
errors_total = Counter(
    "rag_errors_total",
    "Total number of errors",
    ["error_type", "component"],
    registry=registry
)

# Application info
app_info = Info(
    "rag_application_info",
    "RAG application information",
    registry=registry
)

# =============================================================================
# Metrics Collection Class
# =============================================================================

class RAGMetrics:
    """
    Metrics collection class for RAG system.
    """
    
    def __init__(self):
        self.registry = registry
        self._update_app_info()
    
    def _update_app_info(self):
        """Update application info metrics."""
        try:
            from app.core.config import get_settings
            settings = get_settings()
            
            app_info.info({
                "name": settings.app_name,
                "version": settings.app_version,
                "environment": settings.environment
            })
        except Exception as e:
            logger.warning("Failed to update app info", error=str(e))
    
    # HTTP Metrics
    def record_http_request(
        self, 
        method: str, 
        endpoint: str, 
        status_code: int, 
        duration: float
    ):
        """Record HTTP request metrics."""
        http_requests_total.labels(
            method=method,
            endpoint=endpoint,
            status_code=str(status_code)
        ).inc()
        
        http_request_duration_seconds.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    # Document Processing Metrics
    def record_document_processed(
        self, 
        document_type: str, 
        status: str, 
        duration: float
    ):
        """Record document processing metrics."""
        documents_processed_total.labels(
            document_type=document_type,
            status=status
        ).inc()
        
        if status == "success":
            document_processing_duration_seconds.labels(
                document_type=document_type
            ).observe(duration)
    
    def record_embeddings_generated(self, model: str, count: int = 1):
        """Record embedding generation metrics."""
        embeddings_generated_total.labels(model=model).inc(count)
    
    # Vector Search Metrics
    def record_vector_search(
        self, 
        collection: str, 
        duration: float, 
        result_count: int
    ):
        """Record vector search metrics."""
        vector_searches_total.labels(collection=collection).inc()
        vector_search_duration_seconds.labels(collection=collection).observe(duration)
    
    # RAG Pipeline Metrics
    def record_candidate_score(
        self, 
        fit_level: str, 
        duration: float
    ):
        """Record candidate scoring metrics."""
        candidate_scores_generated_total.labels(fit_level=fit_level).inc()
        candidate_scoring_duration_seconds.observe(duration)
    
    def record_email_generated(self, tone: str, duration: float):
        """Record email generation metrics."""
        emails_generated_total.labels(tone=tone).inc()
        email_generation_duration_seconds.labels(tone=tone).observe(duration)
    
    # LLM Metrics
    def record_llm_request(
        self, 
        model: str, 
        operation: str, 
        status: str, 
        duration: float,
        input_tokens: int = 0,
        output_tokens: int = 0
    ):
        """Record LLM request metrics."""
        llm_requests_total.labels(
            model=model,
            operation=operation,
            status=status
        ).inc()
        
        llm_request_duration_seconds.labels(
            model=model,
            operation=operation
        ).observe(duration)
        
        if input_tokens > 0:
            llm_tokens_consumed_total.labels(
                model=model,
                token_type="input"
            ).inc(input_tokens)
        
        if output_tokens > 0:
            llm_tokens_consumed_total.labels(
                model=model,
                token_type="output"
            ).inc(output_tokens)
    
    # System Metrics
    def update_active_users(self, count: int):
        """Update active users gauge."""
        active_users_gauge.set(count)
    
    def update_vector_store_stats(self, collection: str, document_count: int):
        """Update vector store statistics."""
        vector_store_documents_gauge.labels(collection=collection).set(document_count)
    
    def update_database_connections(self, count: int):
        """Update database connections gauge."""
        database_connections_gauge.set(count)
    
    # Background Task Metrics
    def record_celery_task(
        self, 
        task_name: str, 
        status: str, 
        duration: float = None
    ):
        """Record Celery task metrics."""
        celery_tasks_total.labels(
            task_name=task_name,
            status=status
        ).inc()
        
        if duration is not None:
            celery_task_duration_seconds.labels(task_name=task_name).observe(duration)
    
    # Error Metrics
    def record_error(self, error_type: str, component: str):
        """Record error metrics."""
        errors_total.labels(
            error_type=error_type,
            component=component
        ).inc()
    
    # Metrics Export
    def get_metrics(self) -> bytes:
        """Get all metrics in Prometheus format."""
        return generate_latest(self.registry)
    
    def get_metrics_response(self) -> Response:
        """Get metrics as FastAPI response."""
        metrics_data = self.get_metrics()
        return Response(
            content=metrics_data,
            media_type=CONTENT_TYPE_LATEST
        )


# =============================================================================
# Global Metrics Instance
# =============================================================================

_metrics_instance: Optional[RAGMetrics] = None


def get_metrics() -> RAGMetrics:
    """
    Get or create global metrics instance.
    
    Returns:
        RAGMetrics: Metrics collection instance
    """
    global _metrics_instance
    
    if _metrics_instance is None:
        _metrics_instance = RAGMetrics()
    
    return _metrics_instance


# =============================================================================
# Metrics Middleware
# =============================================================================

import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Middleware to collect HTTP request metrics.
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.metrics = get_metrics()
    
    async def dispatch(self, request: Request, call_next):
        """Process request and record metrics."""
        start_time = time.time()
        
        # Process request
        response = await call_next(request)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Record metrics
        self.metrics.record_http_request(
            method=request.method,
            endpoint=self._get_endpoint_name(request),
            status_code=response.status_code,
            duration=duration
        )
        
        return response
    
    def _get_endpoint_name(self, request: Request) -> str:
        """Extract endpoint name from request."""
        path = request.url.path
        
        # Remove API prefix
        if path.startswith("/api/v1"):
            path = path[7:]
        
        # Group similar endpoints
        if path.startswith("/docs") or path.startswith("/redoc"):
            return "/docs"
        elif path == "/openapi.json":
            return "/openapi"
        elif path == "/health":
            return "/health"
        elif path == "/metrics":
            return "/metrics"
        else:
            # Keep first two path segments for grouping
            parts = [p for p in path.split("/") if p]
            if len(parts) >= 2:
                return f"/{parts[0]}/{parts[1]}"
            elif len(parts) == 1:
                return f"/{parts[0]}"
            else:
                return "/"
